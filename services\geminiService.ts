
import { GoogleGenAI, GenerateContentResponse } from '@google/genai';
import type { Topic, Presentation, Slide, NormalizationResult } from '../types';
import { GEMINI_TEXT_MODEL } from '../constants';

// Helper to parse JSON, handling potential markdown fences
const parseJsonFromGeminiResponse = <T,>(responseText: string): T => {
  let jsonStr = responseText.trim();
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = jsonStr.match(fenceRegex);
  if (match && match[2]) {
    jsonStr = match[2].trim();
  }
  try {
    return JSON.parse(jsonStr) as T;
  } catch (e) {
    console.error("Failed to parse JSON response. Raw text:", responseText, "Error:", e);
    throw new Error(`Invalid JSON response from AI: ${(e as Error).message}. Response snippet: ${responseText.substring(0,100)}`);
  }
};


export const extractTopics = async (ai: GoogleGenAI, bookContent: string): Promise<Topic[]> => {
  const prompt = `Analyze the following book content (first 5000 characters) and extract up to 5 main topics.
For each topic, provide a concise title and a brief summary (1-2 sentences).
Return the result *strictly* as a JSON array of objects. Each object in the array MUST have the following three string properties:
1.  'id': A unique identifier string (e.g., "topic-1", "topic-abc-2").
2.  'title': The title of the topic.
3.  'summary': A brief summary of the topic.

The JSON structure must be an array at the top level, like this:
[
  { "id": "example-id-1", "title": "Example Title 1", "summary": "Example summary 1." },
  { "id": "example-id-2", "title": "Example Title 2", "summary": "Example summary 2." }
]

VERY IMPORTANT: The entire response MUST be a single, valid JSON array.
- Do NOT include any text, comments, explanations, or markdown formatting (like \`\`\`) outside of this JSON array.
- Do NOT include any non-English characters or any characters that are not part of standard JSON syntax within the structure of the JSON itself (e.g., within keys, braces, brackets, colons, commas). Values (like title and summary) can be in English.
- Ensure all strings are properly quoted and all necessary commas and braces are present.

Content:
${bookContent.substring(0, 5000)}
`;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: GEMINI_TEXT_MODEL,
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.3,
      },
    });

    console.log("Gemini extractTopics raw response text:", response.text);
    console.log("Gemini extractTopics promptFeedback:", response.promptFeedback);

    if (response.promptFeedback?.blockReason) {
      const blockMessage = `Content generation blocked by AI due to: ${response.promptFeedback.blockReason}. ${response.promptFeedback.blockReasonMessage || ''}`;
      console.error(blockMessage);
      throw new Error(blockMessage);
    }
    
    if (!response.text || response.text.trim() === "") {
        console.warn("Gemini extractTopics returned empty text response. Assuming no topics found.");
        return [];
    }

    const parsedData = parseJsonFromGeminiResponse<any>(response.text); 

    if (!Array.isArray(parsedData)) {
        const errorMessage = `AI returned an unexpected data structure for topics. Expected an array. Received: ${typeof parsedData}. Snippet: ${JSON.stringify(parsedData).substring(0,100)}`;
        console.error(errorMessage, parsedData);
        throw new Error(errorMessage);
    }
    
    const topics = parsedData as Topic[];
    return topics.map((topic, index) => ({ 
        ...topic, 
        id: topic.id || `topic-${Date.now()}-${index + 1}`,
        title: topic.title || "Untitled Topic",
        summary: topic.summary || "No summary provided."
    }));

  } catch (error) {
    console.error("Error in extractTopics calling Gemini or processing response:", error);
    throw error; 
  }
};


export const generatePresentationOutline = async (ai: GoogleGenAI, topicTitle: string, topicContext: string): Promise<Presentation> => {
  const prompt = `
Generate a presentation outline for the topic "${topicTitle}" based on the following context:
Context: "${topicContext.substring(0, 4000)}"

The presentation should have a main title and 3-5 slides.
For each slide, provide:
1.  'slideTitle': A concise title for the slide.
2.  'contentPoints': An array of 2-4 key bullet points (strings) that are insightful and directly derived from the provided context relevant to the slide's theme.
3.  'imageSuggestion': (Optional, string) If an image is highly relevant and can visually enhance this specific slide's content, provide a DETAILED AND DESCRIPTIVE TEXTUAL PROMPT (max 20 words) suitable for an advanced AI image generation model (e.g., "Photorealistic image of a human brain with glowing neural pathways connecting to a futuristic computer interface, symbolizing AI-human collaboration."). The prompt should be specific to the slide's content. If no image significantly adds value or if the content is too abstract for a meaningful image, set this to null. Do not suggest generic images.
4.  'diagramSuggestion': (Optional, object) If a diagram (like a flowchart, mindmap, sequence diagram, etc.) can effectively illustrate a process, relationship, or concept discussed in this specific slide, provide an object with:
    *   'type': (string) The type of diagram (e.g., "flowchart", "mindmap", "sequence diagram", "cycle diagram", "comparison chart").
    *   'description': (string) A clear and concise description of what the diagram should visually represent, detailing the elements and their connections relevant to the slide's content. This description will be used to generate MermaidJS syntax.
    If no diagram is suitable for this slide, set 'diagramSuggestion' to null.
5.  'interaction': (Optional, object) If an interactive element is suitable for a slide (e.g., a quiz on the middle slide, a video on the last slide):
    *   'type': (string) "quiz" or "video".
    *   If 'quiz':
        *   'question': (string) A question relevant to the slide's content.
        *   'options': (array of strings) 3-4 options.
        *   'correctAnswer': (string) One of the options.
    *   If 'video':
        *   'videoUrl': (string) A generic placeholder video URL (e.g., "https://www.w3schools.com/html/mov_bbb.mp4").
        *   'videoSearchQuery': (string) A concise and specific search query (max 15 words) for finding a YouTube video that is highly relevant to THIS SLIDE's content (e.g., "scientific explanation of phenomenon X", "tutorial for concept Y discussed on slide"). This query should enable a user to find a suitable educational video.
    If no interaction is suitable for this slide, set 'interaction' to null.

Return the result *strictly* as a single JSON object. This object MUST have a 'title' property (string, for the overall presentation title, derived from \`topicTitle\`) and a 'slides' property (an array of slide objects as described above).
Example for a slide object:
{
  "slideTitle": "Key Concept Explained",
  "contentPoints": ["Detailed point 1 from context", "Detailed point 2 from context"],
  "imageSuggestion": "Close-up of a magnifying glass focusing on a complex data algorithm display, symbolizing in-depth analysis.",
  "diagramSuggestion": { "type": "flowchart", "description": "A flowchart showing the 3-step process of data analysis: Data Input, Algorithmic Processing, Insight Generation." },
  "interaction": null 
}
Example for a slide with a video interaction:
{
  "slideTitle": "Applications in Real World",
  "contentPoints": ["Application A detailed", "Application B detailed"],
  "imageSuggestion": null,
  "diagramSuggestion": null,
  "interaction": { 
    "type": "video", 
    "videoUrl": "https://www.w3schools.com/html/mov_bbb.mp4",
    "videoSearchQuery": "real world applications of [specific technology from slide]"
  }
}
VERY IMPORTANT: Ensure the entire response is a single, valid JSON object. Do not include any text, comments, or markdown formatting (like \`\`\`) outside of this JSON object itself. Focus on quality and deep relevance of contentPoints and visual suggestions to the provided context.
Use only standard English characters for JSON keys and structure. Ensure all strings are properly quoted and all necessary commas and braces are present.
`;

  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: prompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.6, 
    },
  });
  
  console.log("Gemini generatePresentationOutline raw response text:", response.text);
  console.log("Gemini generatePresentationOutline promptFeedback:", response.promptFeedback);

  if (response.promptFeedback?.blockReason) {
    const blockMessage = `Presentation outline generation blocked due to: ${response.promptFeedback.blockReason}. ${response.promptFeedback.blockReasonMessage || ''}`;
    console.error(blockMessage);
    throw new Error(blockMessage);
  }
  if (!response.text || response.text.trim() === "") {
    console.warn("Gemini generatePresentationOutline returned empty text response.");
    throw new Error("AI returned an empty response for presentation outline.");
  }
  
  const presentation = parseJsonFromGeminiResponse<Presentation>(response.text);
  if (!presentation || !presentation.slides || !Array.isArray(presentation.slides)) {
    const errorMsg = `AI returned an invalid structure for presentation outline. Snippet: ${response.text.substring(0,100)}`;
    console.error(errorMsg, presentation);
    throw new Error(errorMsg);
  }
  // Validate slide structure a bit more
  presentation.slides.forEach(slide => {
    if (!slide.slideTitle || !Array.isArray(slide.contentPoints)) {
        const errorMsg = `AI returned an invalid slide structure. Slide: ${JSON.stringify(slide).substring(0,100)}`;
        console.error(errorMsg, presentation);
        throw new Error(errorMsg);
    }
  });
  return presentation;
};

export const generateMermaidSyntaxForTopic = async (ai: GoogleGenAI, diagramType: string, diagramDescription: string): Promise<string> => {
  const prompt = `
Generate MermaidJS syntax for a "${diagramType}" diagram that illustrates the following: "${diagramDescription.substring(0,1000)}".
The diagram should be clear, well-structured, and accurately represent the described elements and relationships.
Only output the MermaidJS code block. Do not include any other text or explanation.
The response must be only the Mermaid code itself, starting with the appropriate Mermaid graph type declaration (e.g., 'graph TD;', 'mindmap', 'sequenceDiagram', etc.).

For example, if asked for a flowchart for "A simple login process: User enters credentials, system validates, if valid show dashboard, if invalid show error.":
graph TD;
    A[User Enters Credentials] --> B{Validate Credentials};
    B -- Valid --> C[Show Dashboard];
    B -- Invalid --> D[Show Error Message];

Provide the complete and valid MermaidJS syntax. Ensure no markdown formatting (like \`\`\`mermaid) is used around the code block.
The diagram should be reasonably detailed based on the description but avoid excessive complexity unless specified.
  `;
  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: prompt,
    config: { temperature: 0.2 } 
  });
  
  if (response.promptFeedback?.blockReason) {
    throw new Error(`Mermaid syntax generation blocked: ${response.promptFeedback.blockReason}`);
  }
  if (!response.text || response.text.trim() === "") {
    return `graph TD;\nA[Error: No diagram generated for '${diagramDescription.substring(0,30)}...'];`;
  }

  let mermaidCode = response.text.trim();
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = mermaidCode.match(fenceRegex);
  if (match && match[2]) {
    mermaidCode = match[2].trim();
  }
  // A simple validation for mermaid syntax start
  const validMermaidStarters = ['graph', 'flowchart', 'sequenceDiagram', 'gantt', 'classDiagram', 'stateDiagram', 'pie', 'erDiagram', 'mindmap', 'journey'];
  if (!validMermaidStarters.some(starter => mermaidCode.toLowerCase().startsWith(starter))) {
    console.warn(`Generated Mermaid syntax might be invalid or incomplete. Starting with: ${mermaidCode.substring(0,30)}`);
  }
  return mermaidCode;
};

export const normalizeArabicText = async (ai: GoogleGenAI, text: string): Promise<NormalizationResult> => {
  const prompt = `
You are an expert Arabic linguist and proofreader. Your task is to review the following Arabic text.
Normalize it to Modern Standard Arabic (MSA). Correct any clear spelling errors or typos.
If there are colloquial or non-standard phrases, rephrase them into clear MSA, ensuring the original meaning is preserved.
If the text is already high-quality MSA and requires no changes, state that in the analysis.
Focus on clarity and correctness. Avoid overly aggressive changes or altering the core message.

Return the result *strictly* as a single JSON object with three keys:
1.  'originalText': The exact input text you received.
2.  'normalizedText': The processed text. If no changes were made, this should be identical to originalText.
3.  'analysis': A brief (1-2 sentences) description in English of the changes made, or a statement like "The text is already high-quality Modern Standard Arabic and requires no normalization."

Example of the JSON output format:
{
  "originalText": "النص الأصلي هنا...",
  "normalizedText": "النص المُعالج أو المُصحح هنا...",
  "analysis": "Corrected minor spelling errors and standardized a colloquial phrase to MSA."
}

VERY IMPORTANT: The entire response MUST be a single, valid JSON object.
- Do NOT include any text, comments, explanations, or markdown formatting (like \`\`\`) outside of this JSON object.
- Ensure all strings within the JSON are properly quoted (especially Arabic text) and all necessary commas and braces are present.

Input Arabic text (max 2000 characters):
${text.substring(0, 2000)}
`;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: GEMINI_TEXT_MODEL,
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.3, // Lower temperature for more deterministic corrections
      },
    });

    console.log("Gemini normalizeArabicText raw response text:", response.text);

    if (response.promptFeedback?.blockReason) {
      const blockMessage = `Arabic text normalization blocked due to: ${response.promptFeedback.blockReason}. ${response.promptFeedback.blockReasonMessage || ''}`;
      console.error(blockMessage);
      throw new Error(blockMessage);
    }
    if (!response.text || response.text.trim() === "") {
      console.warn("Gemini normalizeArabicText returned empty text response.");
      // Fallback to returning original text with an analysis indicating no processing
      return {
        originalText: text,
        normalizedText: text,
        analysis: "AI returned an empty response. No normalization performed."
      };
    }
    
    const result = parseJsonFromGeminiResponse<NormalizationResult>(response.text);

    // Basic validation of the result structure
    if (typeof result.originalText !== 'string' || typeof result.normalizedText !== 'string' || typeof result.analysis !== 'string') {
      console.error("Invalid structure for NormalizationResult:", result);
      throw new Error("AI returned an invalid structure for Arabic text normalization.");
    }
    
    return result;

  } catch (error) {
    console.error("Error in normalizeArabicText calling Gemini or processing response:", error);
    // Fallback to returning original text with an error analysis
     return {
        originalText: text,
        normalizedText: text,
        analysis: `Error during normalization: ${error instanceof Error ? error.message : String(error)}`
      };
  }
};
