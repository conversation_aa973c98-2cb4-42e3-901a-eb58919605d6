/**
 * Animation controller for AI Interactive Textbook
 * Author: Dr.<PERSON>, SUST-BME
 */

// Animation Manager
const AnimationManager = {
  isReducedMotion: false,
  animationQueue: [],
  
  init() {
    this.checkReducedMotion();
    this.setupIntersectionObserver();
    this.setupParallaxEffects();
    this.setupTypewriterEffects();
    this.setupCounterAnimations();
    this.setupParticleEffects();
    
    console.log('Animation Manager initialized');
  },

  // Check for reduced motion preference
  checkReducedMotion() {
    this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (this.isReducedMotion) {
      document.body.classList.add('reduced-motion');
      console.log('Reduced motion mode enabled');
    }
  },

  // Intersection Observer for scroll animations
  setupIntersectionObserver() {
    if (this.isReducedMotion) return;

    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.triggerAnimation(entry.target);
        }
      });
    }, observerOptions);

    // Observe elements with animation classes
    const animatedElements = document.querySelectorAll(`
      .animate-on-scroll,
      .fade-in-up,
      .fade-in-left,
      .fade-in-right,
      .scale-in,
      .slide-in-up,
      .slide-in-down
    `);

    animatedElements.forEach(el => {
      this.observer.observe(el);
    });
  },

  // Trigger animation based on element class
  triggerAnimation(element) {
    const delay = element.dataset.delay || 0;
    
    setTimeout(() => {
      if (element.classList.contains('fade-in-up')) {
        element.classList.add('animate-fadeInUp');
      } else if (element.classList.contains('fade-in-left')) {
        element.classList.add('animate-fadeInLeft');
      } else if (element.classList.contains('fade-in-right')) {
        element.classList.add('animate-fadeInRight');
      } else if (element.classList.contains('scale-in')) {
        element.classList.add('animate-scaleIn');
      } else if (element.classList.contains('slide-in-up')) {
        element.classList.add('animate-slideInUp');
      } else if (element.classList.contains('slide-in-down')) {
        element.classList.add('animate-slideInDown');
      } else {
        element.classList.add('animate-fadeIn');
      }
      
      element.classList.add('animated');
    }, delay);

    // Unobserve after animation
    this.observer.unobserve(element);
  },

  // Parallax scrolling effects
  setupParallaxEffects() {
    if (this.isReducedMotion) return;

    const parallaxElements = document.querySelectorAll('.parallax');
    
    if (parallaxElements.length === 0) return;

    const handleParallax = () => {
      const scrolled = window.pageYOffset;
      
      parallaxElements.forEach(element => {
        const rate = scrolled * -0.5;
        element.style.transform = `translateY(${rate}px)`;
      });
    };

    window.addEventListener('scroll', 
      window.AppUtils?.Utils?.throttle(handleParallax, 16) || handleParallax
    );
  },

  // Typewriter effect
  setupTypewriterEffects() {
    const typewriterElements = document.querySelectorAll('.typewriter');
    
    typewriterElements.forEach(element => {
      this.typewriterEffect(element);
    });
  },

  typewriterEffect(element) {
    if (this.isReducedMotion) {
      element.style.opacity = '1';
      return;
    }

    const text = element.textContent;
    const speed = parseInt(element.dataset.speed) || 50;
    const delay = parseInt(element.dataset.delay) || 0;
    
    element.textContent = '';
    element.style.opacity = '1';
    
    setTimeout(() => {
      let i = 0;
      const timer = setInterval(() => {
        if (i < text.length) {
          element.textContent += text.charAt(i);
          i++;
        } else {
          clearInterval(timer);
          element.classList.add('typewriter-complete');
        }
      }, speed);
    }, delay);
  },

  // Counter animations
  setupCounterAnimations() {
    const counterElements = document.querySelectorAll('.counter');
    
    const counterObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.animateCounter(entry.target);
          counterObserver.unobserve(entry.target);
        }
      });
    });

    counterElements.forEach(el => counterObserver.observe(el));
  },

  animateCounter(element) {
    if (this.isReducedMotion) {
      element.textContent = element.dataset.target;
      return;
    }

    const target = parseInt(element.dataset.target);
    const duration = parseInt(element.dataset.duration) || 2000;
    const start = parseInt(element.dataset.start) || 0;
    
    let current = start;
    const increment = target / (duration / 16);
    
    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        element.textContent = target.toLocaleString();
        clearInterval(timer);
      } else {
        element.textContent = Math.floor(current).toLocaleString();
      }
    }, 16);
  },

  // Particle effects
  setupParticleEffects() {
    if (this.isReducedMotion) return;

    const particleContainers = document.querySelectorAll('.particles');
    
    particleContainers.forEach(container => {
      this.createParticles(container);
    });
  },

  createParticles(container) {
    const particleCount = parseInt(container.dataset.count) || 50;
    
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle';
      
      // Random position
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
      
      // Random animation delay
      particle.style.animationDelay = Math.random() * 20 + 's';
      
      // Random size
      const size = Math.random() * 4 + 2;
      particle.style.width = size + 'px';
      particle.style.height = size + 'px';
      
      container.appendChild(particle);
    }
  },

  // Stagger animation for lists
  staggerAnimation(elements, animationClass, delay = 100) {
    if (this.isReducedMotion) {
      elements.forEach(el => el.classList.add('animated'));
      return;
    }

    elements.forEach((element, index) => {
      setTimeout(() => {
        element.classList.add(animationClass, 'animated');
      }, index * delay);
    });
  },

  // Hover animations
  setupHoverAnimations() {
    const hoverElements = document.querySelectorAll('.hover-animate');
    
    hoverElements.forEach(element => {
      element.addEventListener('mouseenter', () => {
        if (!this.isReducedMotion) {
          element.classList.add('animate-pulse');
        }
      });
      
      element.addEventListener('mouseleave', () => {
        element.classList.remove('animate-pulse');
      });
    });
  },

  // Loading animations
  showLoadingAnimation(container, type = 'spinner') {
    const loadingHTML = {
      spinner: '<div class="loading-spinner"></div>',
      dots: '<div class="loading-dots"><span></span><span></span><span></span></div>',
      wave: '<div class="loading-wave"><span></span><span></span><span></span><span></span></div>',
      pulse: '<div class="loading-pulse"></div>'
    };

    container.innerHTML = loadingHTML[type] || loadingHTML.spinner;
    container.classList.add('loading-container');
  },

  hideLoadingAnimation(container) {
    container.classList.remove('loading-container');
    container.innerHTML = '';
  },

  // Page transition animations
  pageTransition(direction = 'forward') {
    if (this.isReducedMotion) return Promise.resolve();

    return new Promise(resolve => {
      const body = document.body;
      const transitionClass = direction === 'forward' ? 'page-exit-right' : 'page-exit-left';
      
      body.classList.add(transitionClass);
      
      setTimeout(() => {
        body.classList.remove(transitionClass);
        resolve();
      }, 300);
    });
  },

  // Smooth reveal for content sections
  revealSection(section) {
    if (this.isReducedMotion) {
      section.style.opacity = '1';
      return;
    }

    const elements = section.querySelectorAll('.reveal-item');
    
    elements.forEach((element, index) => {
      setTimeout(() => {
        element.classList.add('animate-fadeInUp');
      }, index * 150);
    });
  },

  // Utility functions
  addBounceEffect(element) {
    if (this.isReducedMotion) return;
    
    element.classList.add('animate-bounce');
    setTimeout(() => {
      element.classList.remove('animate-bounce');
    }, 1000);
  },

  addShakeEffect(element) {
    if (this.isReducedMotion) return;
    
    element.classList.add('animate-shake');
    setTimeout(() => {
      element.classList.remove('animate-shake');
    }, 500);
  },

  addGlowEffect(element) {
    if (this.isReducedMotion) return;
    
    element.classList.add('animate-glow');
  },

  removeGlowEffect(element) {
    element.classList.remove('animate-glow');
  },

  // Cleanup function
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    
    // Clear any running animations
    this.animationQueue.forEach(animation => {
      if (animation.timer) {
        clearInterval(animation.timer);
      }
    });
    
    this.animationQueue = [];
  }
};

// Initialize animations when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  AnimationManager.init();
});

// Handle visibility change to pause/resume animations
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    // Pause animations when tab is not visible
    document.body.classList.add('animations-paused');
  } else {
    // Resume animations when tab becomes visible
    document.body.classList.remove('animations-paused');
  }
});

// Export for global use
window.AnimationManager = AnimationManager;
