
import React, { useState, useCallback, useEffect } from 'react';
import { GoogleGenAI } from '@google/genai';
import type { NormalizationResult } from '../types';
import { normalizeArabicText } from '../services/geminiService';
import { Button } from './common/Button';
import { LoadingSpinner } from './common/LoadingSpinner';

// Simple SVG Icons
const SparklesIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`w-5 h-5 ${className}`}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.25 12L17 14.25l-1.25-2.25L13.5 11l2.25-1.25L17 7.5l1.25 2.25L20.5 11l-2.25 1.25z" />
  </svg>
);

const SpeakIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`w-5 h-5 ${className}`}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z" />
  </svg>
);

interface ArabicTextNormalizerProps {
  text: string; // Current page's original Arabic text
  ai: GoogleGenAI;
}

export const ArabicTextNormalizer: React.FC<ArabicTextNormalizerProps> = ({ text, ai }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<NormalizationResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [displayMode, setDisplayMode] = useState<'original' | 'normalized'>('original');
  const [isSpeaking, setIsSpeaking] = useState(false);

  const handleNormalize = async () => {
    if (!text || !ai) {
      setError("Text or AI service not available.");
      return;
    }
    setIsLoading(true);
    setError(null);
    setResult(null);
    try {
      const normalizationResult = await normalizeArabicText(ai, text);
      setResult(normalizationResult);
      setDisplayMode('normalized'); // Show normalized text by default after processing
    } catch (err) {
      console.error("Error normalizing Arabic text:", err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSpeakNormalized = useCallback(() => {
    if (isSpeaking || !result || !result.normalizedText || !window.speechSynthesis) return;
    
    // Cancel any ongoing speech from this component or globally
    window.speechSynthesis.cancel(); 
    
    setIsSpeaking(true);
    const utterance = new SpeechSynthesisUtterance(result.normalizedText);
    
    // Try to find an Arabic voice
    const voices = window.speechSynthesis.getVoices();
    let arabicVoice = voices.find(v => v.lang.startsWith('ar') && v.default);
    if (!arabicVoice) arabicVoice = voices.find(v => v.lang.startsWith('ar'));
    
    if (arabicVoice) {
      utterance.voice = arabicVoice;
      utterance.lang = arabicVoice.lang;
    } else {
      utterance.lang = 'ar-SA'; // Fallback to a general Arabic locale
    }
    
    utterance.onend = () => setIsSpeaking(false);
    utterance.onerror = (e) => {
      console.error("Speech synthesis error for normalized text:", e);
      setError("Error speaking text: " + e.error);
      setIsSpeaking(false);
    };
    window.speechSynthesis.speak(utterance);
  }, [result, isSpeaking]);

  // Stop local speech if component unmounts or text changes significantly
  useEffect(() => {
    return () => {
      if (window.speechSynthesis && isSpeaking) {
        window.speechSynthesis.cancel();
      }
    };
  }, [isSpeaking, text]); // text dependency ensures if underlying page changes, active speech stops

  const textToShow = displayMode === 'normalized' && result ? result.normalizedText : text;

  return (
    <div className="my-4 p-4 bg-slate-700/50 rounded-lg shadow-md border border-slate-600">
      <h4 className="text-md font-semibold text-sky-300 mb-3">Arabic Text Reviewer</h4>
      
      {isLoading && <div className="py-4"><LoadingSpinner /></div>}
      
      {error && <p className="text-red-400 bg-red-900/50 p-2 rounded-md text-sm my-2">{error}</p>}

      {!isLoading && !result && (
        <Button onClick={handleNormalize} disabled={!text.trim()} className="w-full bg-cyan-600 hover:bg-cyan-500 flex items-center justify-center">
          <SparklesIcon className="mr-2" /> Review & Improve Arabic on This Page
        </Button>
      )}

      {result && (
        <div className="space-y-3">
          <div>
            <h5 className="text-sm font-semibold text-slate-300 mb-1">AI Analysis:</h5>
            <p className="text-xs text-slate-400 bg-slate-600 p-2 rounded-md italic">{result.analysis}</p>
          </div>

          <div className="my-2 flex space-x-2">
            <Button 
              onClick={() => setDisplayMode('original')} 
              className={`text-xs py-1 px-2 ${displayMode === 'original' ? 'bg-sky-600' : 'bg-slate-500 hover:bg-slate-400'}`}
            >
              Show Original
            </Button>
            <Button 
              onClick={() => setDisplayMode('normalized')} 
              className={`text-xs py-1 px-2 ${displayMode === 'normalized' ? 'bg-sky-600' : 'bg-slate-500 hover:bg-slate-400'}`}
              disabled={result.normalizedText === result.originalText && result.analysis.includes("requires no normalization")}
            >
              Show Improved
            </Button>
          </div>
          
          <div 
            className="p-3 bg-slate-800 rounded-md max-h-60 overflow-y-auto text-right whitespace-pre-wrap" 
            dir="rtl" 
            lang="ar"
            aria-label={displayMode === 'normalized' ? "Improved Arabic text" : "Original Arabic text"}
          >
            {textToShow}
          </div>

          <Button 
            onClick={handleSpeakNormalized} 
            disabled={isSpeaking || !result.normalizedText.trim()} 
            className="w-full bg-teal-600 hover:bg-teal-500 flex items-center justify-center text-sm"
          >
            <SpeakIcon className="mr-2" /> 
            {isSpeaking ? 'Speaking...' : 'Read Improved Text Aloud'}
          </Button>
          
          <Button onClick={handleNormalize} className="w-full bg-cyan-700 hover:bg-cyan-600 text-xs mt-2 flex items-center justify-center">
             <SparklesIcon className="mr-2" /> Re-analyze / Re-try
          </Button>
        </div>
      )}
       {!text.trim() && <p className="text-xs text-slate-500 italic text-center mt-2">No text on the current page to analyze.</p>}
    </div>
  );
};
