<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Interactive Textbook</title>

  <!-- SEO and Basic Meta -->
  <meta name="description" content="Enhance your learning with the AI Interactive Textbook. Upload documents (TXT, PDF, DOCX), engage in AI-driven conversations about the content, extract key topics, and generate dynamic presentations. Experience a new way to interact with your textual materials.">
  <meta name="author" content="Dr.<PERSON>, SUST-BME">

  <!-- Theme Color for Mobile Browsers -->
  <meta name="theme-color" content="#0f172a">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://example.com/"> <!-- Replace with actual URL when deployed -->
  <meta property="og:title" content="AI Interactive Textbook">
  <meta property="og:description" content="Enhance your learning with the AI Interactive Textbook. Upload documents, engage in AI-driven conversations, extract key topics, and generate dynamic presentations.">
  <!-- <meta property="og:image" content="https://example.com/og-image.jpg"> --> <!-- Add an actual image URL -->
  <meta property="og:site_name" content="AI Interactive Textbook">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://example.com/"> <!-- Replace with actual URL when deployed -->
  <meta property="twitter:title" content="AI Interactive Textbook">
  <meta property="twitter:description" content="Enhance your learning with the AI Interactive Textbook. Upload documents, engage in AI-driven conversations, extract key topics, and generate dynamic presentations.">
  <!-- <meta property="twitter:image" content="https://example.com/twitter-image.jpg"> --> <!-- Add an actual image URL -->

  <!-- Favicon Placeholders -->
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="manifest" href="/site.webmanifest">


  <!-- External CSS and JS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="styles/main.css">
  <link rel="stylesheet" href="styles/components.css">
  <link rel="stylesheet" href="styles/animations.css">

  <script>
    // pdf.js worker configuration: Define the worker source URL globally and early.
    console.log('Defining window.pdfjsWorkerSrc globally.');
    window.pdfjsWorkerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    console.log('window.pdfjsWorkerSrc defined as:', window.pdfjsWorkerSrc);
  </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
  <script>
    // Eagerly set the workerSrc on pdfjsLib immediately after pdf.min.js has loaded.
    console.log('Attempting eager configuration of PDF.js workerSrc...');
    if (typeof window.pdfjsLib === 'undefined') {
      console.error('CRITICAL PDF.js Setup Error (Eager Config): window.pdfjsLib is NOT defined. PDF.js library might have failed to load or initialize. PDF processing will fail.');
    } else {
      // pdfjsLib IS defined
      console.log('Eager Config: window.pdfjsLib is defined.');
      if (typeof window.pdfjsWorkerSrc === 'undefined') {
        console.error('CRITICAL PDF.js Setup Error (Eager Config): window.pdfjsWorkerSrc is NOT defined. This global variable should be set by a script tag before pdf.min.js. PDF processing will fail.');
      } else {
        // Both pdfjsLib and pdfjsWorkerSrc (the URL string) are defined.
        console.log('Eager Config: window.pdfjsWorkerSrc is defined as:', window.pdfjsWorkerSrc);
        if (typeof window.pdfjsLib.GlobalWorkerOptions === 'object' && window.pdfjsLib.GlobalWorkerOptions !== null) {
          console.log('Eager Config: Setting pdfjsLib.GlobalWorkerOptions.workerSrc.');
          window.pdfjsLib.GlobalWorkerOptions.workerSrc = window.pdfjsWorkerSrc;
          console.log('Eager Config: pdfjsLib.GlobalWorkerOptions.workerSrc successfully set to:', window.pdfjsLib.GlobalWorkerOptions.workerSrc);
        } else {
          console.error('CRITICAL PDF.js Setup Error (Eager Config): window.pdfjsLib.GlobalWorkerOptions is not an object or is null. PDF.js library might be incompletely initialized. Worker path cannot be set. PDF processing will fail.');
        }
      }
    }
  </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
  <style>
    /* Ensure SVGs generated by Mermaid are responsive and styled correctly */
    .mermaid svg {
      max-width: 100%;
      height: auto;
    }

    /* Page transition styles for BookViewer */
    /* The parent of .page-content needs perspective, e.g., style={{ perspective: '1500px' }} */
    .page-content {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0; left: 0;
      transition: transform 0.6s ease-in-out;
      /* transform-style: preserve-3d; */ /* Not strictly needed for single-face flip if backface is hidden */
      backface-visibility: hidden; /* Important for 3D-like flip */
    }

    /* Stable, visible state */
    .page-enter {
      transform: rotateY(0deg);
    }

    /* Flipping to NEXT page (current page flips away to the left) */
    .page-exit-left {
      transform-origin: left center;
      transform: rotateY(-180deg);
    }

    /* New page (for NEXT) starts flipped and rotates in from the right (appearing from behind) */
    .page-enter-from-right {
      transform-origin: left center;
      transform: rotateY(180deg);
    }

    /* Flipping to PREVIOUS page (current page flips away to the right) */
    .page-exit-right {
      transform-origin: right center;
      transform: rotateY(180deg);
    }

    /* New page (for PREVIOUS) starts flipped and rotates in from the left */
    .page-enter-from-left {
      transform-origin: right center;
      transform: rotateY(-180deg);
    }

    /* Presentation Slide Animations */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    .animate-fadeInUp {
      animation: fadeInUp 0.5s ease-out forwards;
    }

    /* Helper for staggered delay - apply inline style like style={{animationDelay: '0.1s'}} */
    .animation-delay-100 { animation-delay: 0.1s !important; }
    .animation-delay-200 { animation-delay: 0.2s !important; }
    .animation-delay-300 { animation-delay: 0.3s !important; }
    .animation-delay-400 { animation-delay: 0.4s !important; }
    .animation-delay-500 { animation-delay: 0.5s !important; }

    /* Custom scrollbar for presentation content if needed */
    .presentation-slide-content::-webkit-scrollbar {
      width: 8px;
    }
    .presentation-slide-content::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
    }
    .presentation-slide-content::-webkit-scrollbar-thumb {
      background: #0ea5e9; /* sky-500 */
      border-radius: 4px;
    }
    .presentation-slide-content::-webkit-scrollbar-thumb:hover {
      background: #0284c7; /* sky-600 */
    }

  </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100">
  <!-- Navigation Bar -->
  <nav id="main-nav" class="fixed top-0 left-0 right-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
    <div class="container mx-auto px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <a href="/" class="text-xl font-bold text-sky-400 hover:text-sky-300 transition-colors">
            AI Interactive Textbook
          </a>
        </div>
        <div class="hidden md:flex items-center space-x-6">
          <a href="/" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">Home</a>
          <a href="pages/features.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">Features</a>
          <a href="pages/documentation.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">Documentation</a>
          <a href="pages/about.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">About</a>
          <a href="pages/contact.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">Contact</a>
        </div>
        <button type="button" id="mobile-menu-btn" class="md:hidden text-slate-300 hover:text-sky-400" title="Toggle mobile menu" aria-label="Toggle mobile menu">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
      <!-- Mobile Menu -->
      <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4 border-t border-slate-700">
        <div class="flex flex-col space-y-2 pt-4">
          <a href="/" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">Home</a>
          <a href="pages/features.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">Features</a>
          <a href="pages/documentation.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">Documentation</a>
          <a href="pages/about.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">About</a>
          <a href="pages/contact.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">Contact</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div id="root" class="pt-20"></div>

  <!-- JavaScript Files -->
  <script type="module" src="/index.tsx"></script>
  <script src="scripts/main.js"></script>
  <script src="scripts/navigation.js"></script>
  <script src="scripts/animations.js"></script>
</body>
</html>
