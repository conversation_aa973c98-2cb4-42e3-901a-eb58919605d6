/**
 * Main JavaScript functionality for AI Interactive Textbook
 * Author: Dr.<PERSON>, SUST-BME
 */

// Global application state
const AppState = {
  currentPage: 'home',
  isLoading: false,
  theme: 'dark',
  animations: true,
  user: null
};

// Utility functions
const Utils = {
  // Debounce function for performance optimization
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // Throttle function for scroll events
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  // Format date for display
  formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(new Date(date));
  },

  // Generate unique ID
  generateId() {
    return Math.random().toString(36).substr(2, 9);
  },

  // Local storage helpers
  storage: {
    set(key, value) {
      try {
        localStorage.setItem(key, JSON.stringify(value));
      } catch (e) {
        console.warn('Failed to save to localStorage:', e);
      }
    },
    get(key) {
      try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
      } catch (e) {
        console.warn('Failed to read from localStorage:', e);
        return null;
      }
    },
    remove(key) {
      try {
        localStorage.removeItem(key);
      } catch (e) {
        console.warn('Failed to remove from localStorage:', e);
      }
    }
  }
};

// Theme management
const ThemeManager = {
  init() {
    const savedTheme = Utils.storage.get('theme') || 'dark';
    this.setTheme(savedTheme);
  },

  setTheme(theme) {
    AppState.theme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    Utils.storage.set('theme', theme);
    this.updateThemeToggle();
  },

  toggleTheme() {
    const newTheme = AppState.theme === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
  },

  updateThemeToggle() {
    const toggleBtn = document.getElementById('theme-toggle');
    if (toggleBtn) {
      toggleBtn.textContent = AppState.theme === 'dark' ? '☀️' : '🌙';
      toggleBtn.setAttribute('aria-label', `Switch to ${AppState.theme === 'dark' ? 'light' : 'dark'} theme`);
    }
  }
};

// Loading state management
const LoadingManager = {
  show(message = 'Loading...') {
    AppState.isLoading = true;
    const loader = document.getElementById('global-loader');
    if (loader) {
      loader.style.display = 'flex';
      const messageEl = loader.querySelector('.loading-message');
      if (messageEl) messageEl.textContent = message;
    }
  },

  hide() {
    AppState.isLoading = false;
    const loader = document.getElementById('global-loader');
    if (loader) {
      loader.style.display = 'none';
    }
  }
};

// Notification system
const NotificationManager = {
  show(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type} animate-slideInDown`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-message">${message}</span>
        <button class="notification-close" aria-label="Close notification">&times;</button>
      </div>
    `;

    // Add to container
    let container = document.getElementById('notification-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'notification-container';
      container.className = 'notification-container';
      document.body.appendChild(container);
    }

    container.appendChild(notification);

    // Auto remove
    const autoRemove = setTimeout(() => {
      this.remove(notification);
    }, duration);

    // Manual close
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
      clearTimeout(autoRemove);
      this.remove(notification);
    });

    return notification;
  },

  remove(notification) {
    notification.classList.add('animate-slideInUp');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  },

  success(message) {
    return this.show(message, 'success');
  },

  error(message) {
    return this.show(message, 'error', 7000);
  },

  warning(message) {
    return this.show(message, 'warning', 6000);
  },

  info(message) {
    return this.show(message, 'info');
  }
};

// Scroll reveal animation
const ScrollReveal = {
  init() {
    this.elements = document.querySelectorAll('.scroll-reveal, .scroll-reveal-left, .scroll-reveal-right');
    this.observer = new IntersectionObserver(this.handleIntersection.bind(this), {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    this.elements.forEach(el => this.observer.observe(el));
  },

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('revealed');
        this.observer.unobserve(entry.target);
      }
    });
  }
};

// Form validation
const FormValidator = {
  validate(form) {
    const errors = [];
    const formData = new FormData(form);

    // Email validation
    const email = formData.get('email');
    if (email && !this.isValidEmail(email)) {
      errors.push('Please enter a valid email address');
    }

    // Required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        errors.push(`${field.getAttribute('data-label') || field.name} is required`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  showErrors(errors) {
    errors.forEach(error => {
      NotificationManager.error(error);
    });
  }
};

// Performance monitoring
const PerformanceMonitor = {
  init() {
    this.measurePageLoad();
    this.measureInteractions();
  },

  measurePageLoad() {
    window.addEventListener('load', () => {
      const perfData = performance.getEntriesByType('navigation')[0];
      const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
      console.log(`Page load time: ${loadTime}ms`);
    });
  },

  measureInteractions() {
    // Measure click response times
    document.addEventListener('click', (e) => {
      const start = performance.now();
      requestAnimationFrame(() => {
        const end = performance.now();
        const duration = end - start;
        if (duration > 16) { // More than one frame
          console.warn(`Slow interaction detected: ${duration}ms`);
        }
      });
    });
  }
};

// Initialize application
document.addEventListener('DOMContentLoaded', () => {
  console.log('AI Interactive Textbook - Initializing...');
  
  // Initialize core systems
  ThemeManager.init();
  ScrollReveal.init();
  PerformanceMonitor.init();

  // Show welcome message
  setTimeout(() => {
    NotificationManager.success('Welcome to AI Interactive Textbook!');
  }, 1000);

  console.log('AI Interactive Textbook - Ready!');
});

// Error handling
window.addEventListener('error', (e) => {
  console.error('Global error:', e.error);
  NotificationManager.error('An unexpected error occurred. Please refresh the page.');
});

window.addEventListener('unhandledrejection', (e) => {
  console.error('Unhandled promise rejection:', e.reason);
  NotificationManager.error('A network error occurred. Please check your connection.');
});

// Export for use in other modules
window.AppUtils = {
  Utils,
  ThemeManager,
  LoadingManager,
  NotificationManager,
  ScrollReveal,
  FormValidator,
  AppState
};
