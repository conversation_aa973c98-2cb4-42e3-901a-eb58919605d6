<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Documentation - AI Interactive Textbook</title>
  
  <!-- SEO and Basic Meta -->
  <meta name="description" content="Complete documentation for AI Interactive Textbook. Learn how to upload documents, interact with AI, generate presentations, and use all features effectively.">
  <meta name="author" content="Dr.<PERSON>, SUST-BME">
  
  <!-- Theme Color for Mobile Browsers -->
  <meta name="theme-color" content="#0f172a">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://example.com/pages/documentation.html">
  <meta property="og:title" content="Documentation - AI Interactive Textbook">
  <meta property="og:description" content="Complete documentation for AI Interactive Textbook. Learn how to upload documents, interact with AI, generate presentations, and use all features effectively.">
  <meta property="og:site_name" content="AI Interactive Textbook">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://example.com/pages/documentation.html">
  <meta property="twitter:title" content="Documentation - AI Interactive Textbook">
  <meta property="twitter:description" content="Complete documentation for AI Interactive Textbook. Learn how to upload documents, interact with AI, generate presentations, and use all features effectively.">

  <!-- Favicon Placeholders -->
  <link rel="apple-touch-icon" sizes="180x180" href="../apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="../favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="../favicon-16x16.png">
  <link rel="manifest" href="../site.webmanifest">

  <!-- External CSS and JS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="../styles/main.css">
  <link rel="stylesheet" href="../styles/components.css">
  <link rel="stylesheet" href="../styles/animations.css">
</head>
<body class="bg-slate-900 text-slate-100">
  <!-- Navigation Bar -->
  <nav id="main-nav" class="fixed top-0 left-0 right-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
    <div class="container mx-auto px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <a href="../index.html" class="text-xl font-bold text-sky-400 hover:text-sky-300 transition-colors">
            AI Interactive Textbook
          </a>
        </div>
        <div class="hidden md:flex items-center space-x-6">
          <a href="../index.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">Home</a>
          <a href="features.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">Features</a>
          <a href="documentation.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors active">Documentation</a>
          <a href="about.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">About</a>
          <a href="contact.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">Contact</a>
        </div>
        <button type="button" id="mobile-menu-btn" class="md:hidden text-slate-300 hover:text-sky-400" title="Toggle mobile menu" aria-label="Toggle mobile menu">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
      <!-- Mobile Menu -->
      <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4 border-t border-slate-700">
        <div class="flex flex-col space-y-2 pt-4">
          <a href="../index.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">Home</a>
          <a href="features.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">Features</a>
          <a href="documentation.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2 active">Documentation</a>
          <a href="about.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">About</a>
          <a href="contact.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">Contact</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="pt-20" id="main-content">
    <!-- Hero Section -->
    <section class="hero">
      <div class="container mx-auto px-4">
        <div class="hero-content">
          <h1 class="hero-title animate-fadeInUp">Documentation</h1>
          <p class="hero-subtitle animate-fadeInUp delay-200">
            Complete guide to using AI Interactive Textbook effectively
          </p>
        </div>
      </div>
    </section>

    <!-- Documentation Content -->
    <section class="section">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- Navigation Sidebar -->
          <aside class="lg:col-span-1">
            <div class="doc-nav fade-in-left">
              <h3 class="doc-nav-title">Table of Contents</h3>
              <ul class="doc-nav-list">
                <li class="doc-nav-item">
                  <a href="#getting-started" class="doc-nav-link">Getting Started</a>
                </li>
                <li class="doc-nav-item">
                  <a href="#uploading-documents" class="doc-nav-link">Uploading Documents</a>
                </li>
                <li class="doc-nav-item">
                  <a href="#ai-interaction" class="doc-nav-link">AI Interaction</a>
                </li>
                <li class="doc-nav-item">
                  <a href="#topic-extraction" class="doc-nav-link">Topic Extraction</a>
                </li>
                <li class="doc-nav-item">
                  <a href="#presentations" class="doc-nav-link">Presentations</a>
                </li>
                <li class="doc-nav-item">
                  <a href="#voice-features" class="doc-nav-link">Voice Features</a>
                </li>
                <li class="doc-nav-item">
                  <a href="#troubleshooting" class="doc-nav-link">Troubleshooting</a>
                </li>
                <li class="doc-nav-item">
                  <a href="#api-reference" class="doc-nav-link">API Reference</a>
                </li>
              </ul>
            </div>
          </aside>

          <!-- Main Documentation Content -->
          <div class="lg:col-span-3">
            <div class="doc-content fade-in-right">
              <!-- Getting Started -->
              <div id="getting-started" class="doc-section">
                <h2>Getting Started</h2>
                <p>Welcome to AI Interactive Textbook! This powerful tool transforms your documents into interactive learning experiences using advanced AI technology.</p>
                
                <h3>Prerequisites</h3>
                <ul>
                  <li>A modern web browser (Chrome, Firefox, Safari, Edge)</li>
                  <li>Internet connection for AI processing</li>
                  <li>Documents in supported formats (TXT, PDF, DOCX)</li>
                </ul>

                <div class="alert alert-info">
                  <strong>Note:</strong> This application requires a Google Gemini API key to function. The API key should be configured in the environment variables.
                </div>
              </div>

              <!-- Uploading Documents -->
              <div id="uploading-documents" class="doc-section">
                <h2>Uploading Documents</h2>
                <p>The first step is to upload your document for analysis and interaction.</p>

                <h3>Supported Formats</h3>
                <ul>
                  <li><strong>TXT files:</strong> Plain text documents</li>
                  <li><strong>PDF files:</strong> Portable Document Format with text extraction</li>
                  <li><strong>DOCX files:</strong> Microsoft Word documents</li>
                </ul>

                <h3>Upload Process</h3>
                <ol>
                  <li>Click the "Choose File" button in the upload area</li>
                  <li>Select your document from the file browser</li>
                  <li>Wait for the document to be processed</li>
                  <li>Review the extracted content and proceed to analysis</li>
                </ol>

                <div class="code-block">
                  // Example of supported file types
                  const supportedTypes = [
                    'text/plain',
                    'application/pdf',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                  ];
                </div>
              </div>

              <!-- AI Interaction -->
              <div id="ai-interaction" class="doc-section">
                <h2>AI Interaction</h2>
                <p>Engage with your document content through intelligent AI conversations.</p>

                <h3>Text Selection</h3>
                <p>Select any portion of text in your document to enable focused AI interaction:</p>
                <ol>
                  <li>Highlight the text you want to discuss</li>
                  <li>The AI interaction panel will become available</li>
                  <li>Ask questions or request explanations about the selected content</li>
                </ol>

                <h3>Conversation Features</h3>
                <ul>
                  <li>Context-aware responses based on document content</li>
                  <li>Follow-up questions and clarifications</li>
                  <li>Explanations of complex concepts</li>
                  <li>Related topic suggestions</li>
                </ul>

                <div class="alert alert-success">
                  <strong>Tip:</strong> For best results, select specific paragraphs or sections rather than entire pages.
                </div>
              </div>

              <!-- Topic Extraction -->
              <div id="topic-extraction" class="doc-section">
                <h2>Topic Extraction</h2>
                <p>Automatically identify and extract key topics from your documents.</p>

                <h3>How It Works</h3>
                <p>The AI analyzes your document structure and content to identify:</p>
                <ul>
                  <li>Main topics and themes</li>
                  <li>Key concepts and definitions</li>
                  <li>Important relationships between ideas</li>
                  <li>Summary information for each topic</li>
                </ul>

                <h3>Using Extracted Topics</h3>
                <ol>
                  <li>Review the automatically generated topic list</li>
                  <li>Click on any topic to generate a presentation</li>
                  <li>Use topics as conversation starters with the AI</li>
                  <li>Export topic summaries for study notes</li>
                </ol>
              </div>

              <!-- Presentations -->
              <div id="presentations" class="doc-section">
                <h2>Presentations</h2>
                <p>Transform extracted topics into dynamic, visual presentations.</p>

                <h3>Presentation Features</h3>
                <ul>
                  <li>Automatically generated slides based on topic content</li>
                  <li>Visual elements including diagrams and images</li>
                  <li>Interactive elements and multimedia content</li>
                  <li>Navigation controls for slide management</li>
                </ul>

                <h3>Customization Options</h3>
                <p>Presentations include various visual enhancements:</p>
                <ul>
                  <li><strong>Mermaid Diagrams:</strong> Flowcharts, mind maps, and process diagrams</li>
                  <li><strong>Contextual Images:</strong> Relevant visual content to support learning</li>
                  <li><strong>Interactive Elements:</strong> Quizzes, polls, and engagement tools</li>
                </ul>

                <div class="code-block">
                  // Example Mermaid diagram syntax
                  graph TD
                      A[Document Upload] --> B[Text Extraction]
                      B --> C[AI Analysis]
                      C --> D[Topic Identification]
                      D --> E[Presentation Generation]
                </div>
              </div>

              <!-- Voice Features -->
              <div id="voice-features" class="doc-section">
                <h2>Voice Features</h2>
                <p>Experience hands-free interaction with voice commands and text-to-speech.</p>

                <h3>Text-to-Speech</h3>
                <ul>
                  <li>Listen to document content with natural voice synthesis</li>
                  <li>Adjustable reading speed and voice selection</li>
                  <li>Pause, resume, and navigation controls</li>
                  <li>Highlight synchronization with audio playback</li>
                </ul>

                <h3>Voice Interaction (VAPI)</h3>
                <ul>
                  <li>Natural language voice commands</li>
                  <li>Voice-based Q&A about document content</li>
                  <li>Hands-free navigation and control</li>
                  <li>Real-time voice processing and responses</li>
                </ul>

                <div class="alert alert-warning">
                  <strong>Note:</strong> Voice features require microphone permissions and a stable internet connection.
                </div>
              </div>

              <!-- Troubleshooting -->
              <div id="troubleshooting" class="doc-section">
                <h2>Troubleshooting</h2>
                <p>Common issues and their solutions.</p>

                <h3>Document Upload Issues</h3>
                <ul>
                  <li><strong>File not supported:</strong> Ensure your file is in TXT, PDF, or DOCX format</li>
                  <li><strong>Large file size:</strong> Try compressing or splitting large documents</li>
                  <li><strong>Corrupted file:</strong> Verify the file opens correctly in other applications</li>
                </ul>

                <h3>AI Processing Errors</h3>
                <ul>
                  <li><strong>API key issues:</strong> Contact administrator to verify API configuration</li>
                  <li><strong>Network errors:</strong> Check internet connection and try again</li>
                  <li><strong>Processing timeout:</strong> Try with smaller document sections</li>
                </ul>

                <h3>Performance Issues</h3>
                <ul>
                  <li><strong>Slow loading:</strong> Clear browser cache and refresh the page</li>
                  <li><strong>Memory issues:</strong> Close other browser tabs and applications</li>
                  <li><strong>Audio problems:</strong> Check browser audio permissions and settings</li>
                </ul>
              </div>

              <!-- API Reference -->
              <div id="api-reference" class="doc-section">
                <h2>API Reference</h2>
                <p>Technical reference for developers and advanced users.</p>

                <h3>Core Services</h3>
                <ul>
                  <li><strong>File Parser Service:</strong> Handles document processing and text extraction</li>
                  <li><strong>Gemini Service:</strong> Manages AI interactions and content generation</li>
                  <li><strong>Voice Service:</strong> Controls text-to-speech and voice recognition</li>
                </ul>

                <h3>Configuration</h3>
                <div class="code-block">
                  // Environment variables required
                  API_KEY=your_google_gemini_api_key
                  
                  // Optional configurations
                  MAX_FILE_SIZE=10MB
                  SUPPORTED_LANGUAGES=en,ar
                  TTS_VOICE=en-US-Standard-A
                </div>

                <div class="alert alert-info">
                  <strong>Developer Note:</strong> For detailed API documentation and integration guides, please refer to the source code documentation.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-slate-800 border-t border-slate-700 py-8">
    <div class="container mx-auto px-4 text-center">
      <div class="text-slate-400">
        <p>&copy; 2024 AI Interactive Textbook. Created by Dr.Mohammed Yagoub Esmail, SUST-BME</p>
        <p class="mt-2">Email: <EMAIL> | Phone: +************, +************</p>
      </div>
    </div>
  </footer>

  <!-- JavaScript Files -->
  <script src="../scripts/main.js"></script>
  <script src="../scripts/navigation.js"></script>
  <script src="../scripts/animations.js"></script>
</body>
</html>
