
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from './common/Button';

// SVG Icons for Speak/Stop
const SpeakIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`w-5 h-5 ${className}`}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z" />
  </svg>
);

const StopIconSvg: React.FC<{ className?: string }> = ({ className }) => ( // Renamed to avoid conflict
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`w-5 h-5 ${className}`}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z" />
  </svg>
);

interface DocumentSpeakerProps {
  textToSpeak: string | null;
  documentName: string | null;
}

type PreferredLanguage = 'en' | 'ar';

export const DocumentSpeaker: React.FC<DocumentSpeakerProps> = ({ textToSpeak, documentName }) => {
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [canSpeak, setCanSpeak] = useState(false);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [selectedVoiceURI, setSelectedVoiceURI] = useState<string>('');
  const [preferredLanguage, setPreferredLanguage] = useState<PreferredLanguage>('en');
  const [statusMessage, setStatusMessage] = useState<string>('');
  
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

  const populateVoiceList = useCallback(() => {
    if (!window.speechSynthesis) return;
    const voices = window.speechSynthesis.getVoices();
    setAvailableVoices(voices);

    if (voices.length > 0) {
      let defaultVoice = voices.find(voice => voice.lang.startsWith(preferredLanguage) && voice.default);
      if (!defaultVoice) {
        defaultVoice = voices.find(voice => voice.lang.startsWith(preferredLanguage));
      }
      if (!defaultVoice && preferredLanguage === 'ar') {
         defaultVoice = voices.find(voice => voice.lang.toLowerCase().includes('ar'));
      }
      if (!defaultVoice) {
        defaultVoice = voices.find(voice => voice.default && voice.lang.startsWith('en'));
         if (!defaultVoice) defaultVoice = voices.find(voice => voice.lang.startsWith('en'));
         if (!defaultVoice) defaultVoice = voices[0];
      }

      if (defaultVoice) {
        setSelectedVoiceURI(defaultVoice.voiceURI);
      } else {
        setSelectedVoiceURI('');
      }
      setStatusMessage('');
    } else {
        setStatusMessage('No voices available in your browser.');
    }
  }, [preferredLanguage]);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.speechSynthesis) {
      setCanSpeak(true);
      populateVoiceList();
      window.speechSynthesis.onvoiceschanged = populateVoiceList;
    } else {
      setCanSpeak(false);
      setStatusMessage("Speech Synthesis API not supported in this browser.");
    }

    return () => {
      if (window.speechSynthesis) {
        // Only cancel if this component's utterance is speaking
        if (utteranceRef.current && window.speechSynthesis.speaking) {
             // Check if the utterance being spoken is the one from this component.
             // This is hard without direct access to the currently speaking utterance object.
             // A simple cancel might be too broad if other components also use TTS.
             // For now, rely on each component cancelling before it speaks.
        }
        window.speechSynthesis.onvoiceschanged = null;
      }
    };
  }, [populateVoiceList]);

  useEffect(() => {
    // This effect specifically handles cancellation if the text or voice/lang changes
    // while *this* component's utterance was speaking.
    if (utteranceRef.current && window.speechSynthesis.speaking) {
        // A more targeted check could be: if (window.speechSynthesis.utterance === utteranceRef.current)
        // However, speechSynthesis.utterance is not a standard API.
        // So, if this speaker *thinks* it's speaking, but text/voice changed, it should stop.
        window.speechSynthesis.cancel(); 
        setIsSpeaking(false); // Reflect that its own speech attempt was cancelled
        utteranceRef.current = null;
    }
  }, [textToSpeak, selectedVoiceURI, preferredLanguage]);


  const handleToggleSpeech = useCallback(() => {
    if (!canSpeak || !textToSpeak) {
        setStatusMessage(textToSpeak ? "Cannot initiate speech." : "No text to speak.");
        return;
    }

    // Always cancel any existing speech before starting new speech from this component.
    // This ensures other TTS (like paragraph TTS) is stopped.
    if (window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel();
    }
    // Allow a brief moment for cancel to process before starting new speech
    setTimeout(() => {
        if (isSpeaking) { // This means the button was 'Stop'
            // Speech should have been cancelled by the general cancel above or onend.
            // This state might be slightly out of sync, setIsSpeaking(false) will be called by onend.
            // If for some reason onend didn't fire, this ensures we try to reset.
            utteranceRef.current = null; 
            setIsSpeaking(false);
        } else { // This means the button was 'Speak'
          const utterance = new SpeechSynthesisUtterance(textToSpeak);
          utteranceRef.current = utterance; // Store this component's utterance

          const selectedVoice = availableVoices.find(v => v.voiceURI === selectedVoiceURI);

          if (selectedVoice) {
            utterance.voice = selectedVoice;
            utterance.lang = selectedVoice.lang;
          } else {
            utterance.lang = preferredLanguage === 'ar' ? 'ar-SA' : 'en-US'; 
          }
          
          utterance.onstart = () => setIsSpeaking(true);
          utterance.onend = () => {
            setIsSpeaking(false);
            if (utteranceRef.current === utterance) { // Ensure it's the same utterance
                utteranceRef.current = null;
            }
          };
          utterance.onerror = (event) => {
            console.error("DocumentSpeaker speech synthesis error:", event.error, event);
            setStatusMessage(`Speech error: ${event.error}`);
            setIsSpeaking(false);
             if (utteranceRef.current === utterance) {
                utteranceRef.current = null;
            }
          };
          window.speechSynthesis.speak(utterance);
        }
    }, 50); // 50ms delay
  }, [canSpeak, textToSpeak, selectedVoiceURI, availableVoices, preferredLanguage, isSpeaking]);

  useEffect(() => {
    const interval = setInterval(() => {
      if (window.speechSynthesis) {
        // Only update if no utterance is active from this component, or if it matches the global state
        if (!utteranceRef.current || window.speechSynthesis.speaking === isSpeaking) {
           // If this component doesn't think it's speaking, but global is, don't set isSpeaking to true.
           // This tries to keep DocumentSpeaker's `isSpeaking` relevant to *its own* actions.
           if (window.speechSynthesis.speaking && utteranceRef.current) {
             setIsSpeaking(true);
           } else if (!window.speechSynthesis.speaking && isSpeaking) {
             // If global speech stopped and this component thought it was speaking, update.
             setIsSpeaking(false);
             utteranceRef.current = null; 
           }
        }
      }
    }, 250);
    return () => clearInterval(interval);
  }, [isSpeaking]);

  const filteredVoices = availableVoices.filter(voice => voice.lang.startsWith(preferredLanguage));
  const otherVoices = availableVoices.filter(voice => !voice.lang.startsWith(preferredLanguage));

  if (!canSpeak && statusMessage) {
    return (
      <div className="p-3 bg-slate-700 rounded-lg shadow-md text-center">
        <p className="text-sm text-amber-400">{statusMessage || "Text-to-Speech is not supported."}</p>
      </div>
    );
  }
  
  const buttonDisabled = !textToSpeak || !documentName || (availableVoices.length === 0 && !selectedVoiceURI); // Allow if browser default might work

  return (
    <div className="p-4 bg-slate-700 rounded-lg shadow-md space-y-3">
      <h3 className="text-lg font-semibold text-sky-300">Document Reader</h3>
      
      {documentName && <p className="text-xs text-slate-400">File: <span className="font-medium">{documentName}</span></p>}
      {!documentName && <p className="text-xs text-slate-500 italic">No document loaded to read.</p>}

      <div className="space-y-2">
        <label htmlFor="tts-language" className="block text-sm font-medium text-slate-300">Preferred Language:</label>
        <select
          id="tts-language"
          value={preferredLanguage}
          onChange={(e) => setPreferredLanguage(e.target.value as PreferredLanguage)}
          className="w-full p-2 bg-slate-600 text-slate-100 border border-slate-500 rounded-md focus:ring-sky-500 focus:border-sky-500"
          disabled={isSpeaking}
        >
          <option value="en">English</option>
          <option value="ar">Arabic (العربية)</option>
        </select>
      </div>

      {availableVoices.length > 0 && (
        <div className="space-y-2">
          <label htmlFor="tts-voice" className="block text-sm font-medium text-slate-300">Voice:</label>
          <select
            id="tts-voice"
            value={selectedVoiceURI}
            onChange={(e) => setSelectedVoiceURI(e.target.value)}
            className="w-full p-2 bg-slate-600 text-slate-100 border border-slate-500 rounded-md focus:ring-sky-500 focus:border-sky-500"
            disabled={isSpeaking}
          >
            <option value="">Browser Default ({preferredLanguage.toUpperCase()})</option>
            {filteredVoices.length > 0 && <optgroup label={`Voices for ${preferredLanguage === 'ar' ? 'Arabic' : 'English'}`}>
              {filteredVoices.map(voice => (
                <option key={voice.voiceURI} value={voice.voiceURI}>
                  {voice.name} ({voice.lang})
                </option>
              ))}
            </optgroup>}
            {otherVoices.length > 0 && <optgroup label="Other Available Voices">
              {otherVoices.map(voice => (
                <option key={voice.voiceURI} value={voice.voiceURI}>
                  {voice.name} ({voice.lang})
                </option>
              ))}
            </optgroup>}
          </select>
        </div>
      )}
      
      {statusMessage && <p className="text-xs text-amber-400 text-center">{statusMessage}</p>}

      <Button
        onClick={handleToggleSpeech}
        disabled={buttonDisabled}
        className={`w-full flex items-center justify-center transition-colors duration-150 ${
          isSpeaking ? 'bg-red-600 hover:bg-red-500' : 'bg-teal-600 hover:bg-teal-500'
        }`}
        aria-live="polite"
        aria-label={isSpeaking ? `Stop reading ${documentName || 'document'}` : `Read ${documentName || 'document'} aloud`}
      >
        {isSpeaking ? <StopIconSvg className="mr-2" /> : <SpeakIcon className="mr-2" />}
        {isSpeaking ? 'Stop Reading' : 'Read Document Aloud'}
      </Button>
    </div>
  );
};
