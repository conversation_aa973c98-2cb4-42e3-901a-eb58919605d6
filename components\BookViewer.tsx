
import React, { useCallback, useState, useEffect, useMemo, useRef } from 'react';
import { CHARACTERS_PER_PAGE } from '../constants';
import type { ParsedFileResult, ParsedPageImage } from '../types';
import { Button } from './common/Button';
import { ArabicTextNormalizer } from './ArabicTextNormalizer';
import { GoogleGenAI } from '@google/genai';

// SVG Icons for contextual TTS
const PlayIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={`w-5 h-5 ${className}`}>
    <path d="M6.3 2.841A1.5 1.5 0 0 0 4 4.118v11.764a1.5 1.5 0 0 0 2.3 1.277l9.344-5.882a1.5 1.5 0 0 0 0-2.553L6.3 2.84Z" />
  </svg>
);

const StopIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={`w-5 h-5 ${className}`}>
    <path d="M5.25 3A2.25 2.25 0 0 0 3 5.25v9.5A2.25 2.25 0 0 0 5.25 17h9.5A2.25 2.25 0 0 0 17 14.75v-9.5A2.25 2.25 0 0 0 14.75 3h-9.5Z" />
  </svg>
);


interface BookViewerProps {
  fileResult: ParsedFileResult | null;
  onTextSelect: (selectedText: string) => void;
  aiInstance: GoogleGenAI | null;
}

const isArabicUnicode = (char: string): boolean => {
    const charCode = char.charCodeAt(0);
    return (charCode >= 0x0600 && charCode <= 0x06FF) || 
           (charCode >= 0x0750 && charCode <= 0x077F) || 
           (charCode >= 0x08A0 && charCode <= 0x08FF) || 
           (charCode >= 0xFB50 && charCode <= 0xFDFF) || 
           (charCode >= 0xFE70 && charCode <= 0xFEFF);  
};

const isPredominantlyArabic = (text: string, sampleSize: number = 300, threshold: number = 0.4): boolean => {
    if (!text) return false;
    const sample = text.substring(0, Math.min(text.length, sampleSize));
    if (sample.trim() === "") return false;
    let arabicCharCount = 0;
    let significantCharCount = 0; 
    for (const char of sample) {
        if (char.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\w]/i)) { 
            significantCharCount++;
            if (isArabicUnicode(char)) arabicCharCount++;
        }
    }
    if (significantCharCount === 0) return false; 
    return (arabicCharCount / significantCharCount) >= threshold;
};


export const BookViewer: React.FC<BookViewerProps> = ({ fileResult, onTextSelect, aiInstance }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [displayPage, setDisplayPage] = useState(0);
  const [pagedItems, setPagedItems] = useState<(string | ParsedPageImage)[]>([]);
  const [totalPages, setTotalPages] = useState(0);
  const [animationClass, setAnimationClass] = useState('page-enter');
  const [textContentDirection, setTextContentDirection] = useState<'ltr' | 'rtl'>('ltr');
  const [textContentLang, setTextContentLang] = useState<string>('en');

  // State for contextual paragraph TTS
  const [speakingParagraphKey, setSpeakingParagraphKey] = useState<string | null>(null);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const currentUtteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

  const populateVoices = useCallback(() => {
    if (window.speechSynthesis) {
        setAvailableVoices(window.speechSynthesis.getVoices());
    }
  }, []);

  useEffect(() => {
    populateVoices();
    if (window.speechSynthesis) {
        window.speechSynthesis.onvoiceschanged = populateVoices;
    }
    return () => {
        if (window.speechSynthesis) {
            window.speechSynthesis.onvoiceschanged = null;
            if (window.speechSynthesis.speaking) {
                window.speechSynthesis.cancel();
            }
        }
        currentUtteranceRef.current = null;
    };
  }, [populateVoices]);


  useEffect(() => {
    if (window.speechSynthesis && window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel(); // Cancel any speech when file result changes
    }
    setSpeakingParagraphKey(null); // Reset speaking state

    if (fileResult) {
      let items: (string | ParsedPageImage)[] = [];
      if (fileResult.type === 'text') {
        const pagesArray: string[] = [];
        let remainingContent = fileResult.content.trim();
        while (remainingContent.length > 0) {
          let pageText = remainingContent.substring(0, CHARACTERS_PER_PAGE);
          if (remainingContent.length > CHARACTERS_PER_PAGE) {
            const lastParagraph = pageText.lastIndexOf('\n\n');
            if (lastParagraph > CHARACTERS_PER_PAGE / 2 && lastParagraph > 0) {
              pageText = remainingContent.substring(0, lastParagraph + 2);
            } else {
              const lastSentence = pageText.lastIndexOf('. ');
              const lastArabicSentence = pageText.lastIndexOf('۔');
              const bestLastSentence = Math.max(lastSentence, lastArabicSentence);
              if (bestLastSentence > CHARACTERS_PER_PAGE / 2 && bestLastSentence > 0) {
                pageText = remainingContent.substring(0, bestLastSentence + 1);
              }
            }
          }
          pagesArray.push(pageText);
          remainingContent = remainingContent.substring(pageText.length).trimStart();
        }
        items = pagesArray;

        if (isPredominantlyArabic(fileResult.content)) {
          setTextContentDirection('rtl');
          setTextContentLang('ar');
        } else {
          setTextContentDirection('ltr');
          setTextContentLang('en');
        }

      } else if (fileResult.type === 'pdf_visual') {
        items = fileResult.pages;
        setTextContentDirection('ltr'); 
        setTextContentLang('en'); 
      }

      setPagedItems(items);
      setTotalPages(items.length);
      setCurrentPage(0);
      setDisplayPage(0);
      setAnimationClass('page-enter');
    } else {
      setPagedItems([]);
      setTotalPages(0);
      setCurrentPage(0);
      setDisplayPage(0);
      setAnimationClass('page-enter');
      setTextContentDirection('ltr');
      setTextContentLang('en');
    }
  }, [fileResult]);

  const handleMouseUp = useCallback(() => {
    if (fileResult?.type === 'text') {
      const selection = window.getSelection();
      if (selection && selection.toString().trim().length > 0) {
        onTextSelect(selection.toString().trim());
      }
    } else {
      onTextSelect('');
    }
  }, [onTextSelect, fileResult]);

  const changePage = (newPageIndex: number, direction: 'next' | 'prev') => {
    if (window.speechSynthesis && window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel(); // Cancel speech when changing pages
    }
    setSpeakingParagraphKey(null);

    if (direction === 'next') setAnimationClass('page-exit-left');
    else setAnimationClass('page-exit-right');

    setTimeout(() => {
      setCurrentPage(newPageIndex);
      setDisplayPage(newPageIndex);
      if (direction === 'next') setAnimationClass('page-enter-from-right');
      else setAnimationClass('page-enter-from-left');
      requestAnimationFrame(() => {
        requestAnimationFrame(() => setAnimationClass('page-enter'));
      });
    }, 600);
  };

  const goToNextPage = () => {
    if (currentPage < totalPages - 1) changePage(currentPage + 1, 'next');
  };

  const goToPrevPage = () => {
    if (currentPage > 0) changePage(currentPage - 1, 'prev');
  };
  
  const handleToggleParagraphSpeech = useCallback((paragraphText: string, paragraphKey: string) => {
    if (!window.speechSynthesis) return;

    if (speakingParagraphKey === paragraphKey) { // If current paragraph is speaking, stop it
      window.speechSynthesis.cancel();
      setSpeakingParagraphKey(null);
      currentUtteranceRef.current = null;
    } else { // Play new paragraph
      window.speechSynthesis.cancel(); // Stop any other speech first
      
      const utterance = new SpeechSynthesisUtterance(paragraphText);
      currentUtteranceRef.current = utterance;

      let voiceToUse: SpeechSynthesisVoice | undefined;
      if (textContentLang === 'ar') {
        voiceToUse = availableVoices.find(v => v.lang.startsWith('ar') && v.default) || availableVoices.find(v => v.lang.startsWith('ar'));
        utterance.lang = voiceToUse ? voiceToUse.lang : 'ar-SA';
      } else {
        voiceToUse = availableVoices.find(v => v.lang.startsWith('en') && v.default) || availableVoices.find(v => v.lang.startsWith('en'));
        utterance.lang = voiceToUse ? voiceToUse.lang : 'en-US';
      }
      if (voiceToUse) utterance.voice = voiceToUse;

      utterance.onstart = () => setSpeakingParagraphKey(paragraphKey);
      utterance.onend = () => {
        setSpeakingParagraphKey(null);
        currentUtteranceRef.current = null;
      };
      utterance.onerror = (e) => {
        console.error("Paragraph speech error:", e);
        setSpeakingParagraphKey(null);
        currentUtteranceRef.current = null;
      };
      window.speechSynthesis.speak(utterance);
    }
  }, [speakingParagraphKey, textContentLang, availableVoices]);

  // Cleanup ongoing speech if component unmounts
  useEffect(() => {
    return () => {
        if (window.speechSynthesis && window.speechSynthesis.speaking && currentUtteranceRef.current) {
            // Only cancel if the currentUtteranceRef matches what might be speaking,
            // to avoid cancelling DocumentSpeaker's speech unintentionally too broadly.
            // However, a simple cancel() is usually fine.
            window.speechSynthesis.cancel();
        }
    };
  }, []);


  const currentItemForDisplay = pagedItems[displayPage];
  const isPdfVisual = fileResult?.type === 'pdf_visual';
  
  const paragraphsForDisplay = useMemo(() => {
    if (fileResult?.type === 'text' && typeof currentItemForDisplay === 'string') {
        // Split by one or more newlines, and filter out empty strings from multiple newlines.
        return currentItemForDisplay.split(/\n+/).filter(p => p.trim() !== '');
    }
    return [];
  }, [fileResult, currentItemForDisplay]);


  if (!fileResult && totalPages === 0) {
    return (
         <div className="flex flex-col items-center justify-center h-full text-slate-400">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-24 h-24 mb-4 opacity-50">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6-2.292m0 0V21M12 6.042A8.967 8.967 0 0 1 18 3.75m-6 2.292V3.75m0 2.292A8.966 8.966 0 0 0 6 3.75m6 2.292V21m0 0V3.75M3 12h18M3 12a8.967 8.967 0 0 1 0-4.5m18 4.5a8.967 8.967 0 0 0 0-4.5m0 4.5V7.5m0 4.5v4.5m0-4.5H3m0 0V7.5m0 4.5v4.5" />
            </svg>
            <p className="text-xl">Content will appear here once a document is processed.</p>
        </div>
    );
  }
  
  if (fileResult && totalPages === 0 && pagedItems.length === 0) {
     return (
        <div className="flex flex-col items-center justify-center h-full text-slate-400">
            <p className="text-xl">The document appears to be empty or could not be paginated.</p>
        </div>
     );
  }

  return (
    <div className="space-y-4 flex flex-col h-full">
      <h2 className="text-2xl font-semibold text-sky-300 border-b border-sky-700 pb-2">
        Document Content: {fileResult?.fileName || 'Loading...'}
      </h2>
      
      <div 
        className="flex-grow overflow-hidden relative bg-slate-700 rounded-lg shadow" 
        style={{ perspective: '1500px' }}
        onMouseUp={handleMouseUp} // For text selection for VoiceAgent
      >
        {totalPages > 0 && currentItemForDisplay && (
            <div
              key={displayPage} 
              className={`page-content ${animationClass} ${isPdfVisual ? 'flex items-center justify-center' : 'prose prose-invert max-w-none p-6 text-slate-200 selection:bg-sky-500 selection:text-white'}`}
              style={isPdfVisual ? { width: '100%', height: '100%' } : { whiteSpace: 'pre-wrap', wordWrap: 'break-word', overflowY: 'auto', height: '100%' }}
              dir={!isPdfVisual ? textContentDirection : undefined}
              lang={!isPdfVisual ? textContentLang : undefined}
              aria-label={`Page ${displayPage + 1} of document content. ${!isPdfVisual && textContentLang === 'ar' ? 'Content is in Arabic.' : ''}`}
            >
              {isPdfVisual && typeof currentItemForDisplay === 'object' ? (
                <img 
                  src={(currentItemForDisplay as ParsedPageImage).dataUrl} 
                  alt={`Page ${displayPage + 1} of ${fileResult?.fileName}`} 
                  className="max-w-full max-h-full object-contain"
                />
              ) : fileResult?.type === 'text' && paragraphsForDisplay.length > 0 ? (
                // Render paragraphs with individual play/stop buttons
                <div>
                  {paragraphsForDisplay.map((paragraph, index) => {
                    const paragraphKey = `${displayPage}-${index}`;
                    const isThisParagraphSpeaking = speakingParagraphKey === paragraphKey;
                    if (!paragraph.trim()) return null; // Skip rendering for empty paragraphs

                    return (
                      <div key={paragraphKey} className="relative group my-2" 
                        dir={textContentDirection} lang={textContentLang}
                      >
                        <p className="inline">{paragraph}</p>
                        <Button
                          onClick={() => handleToggleParagraphSpeech(paragraph, paragraphKey)}
                          className={`absolute p-1 rounded-full opacity-30 group-hover:opacity-100 focus:opacity-100 transition-opacity
                            ${textContentDirection === 'rtl' ? 'left-1 top-1' : 'right-1 top-1'} 
                            ${isThisParagraphSpeaking ? 'bg-red-500 hover:bg-red-400' : 'bg-sky-600 hover:bg-sky-500'}`}
                          title={isThisParagraphSpeaking ? "Stop reading this paragraph" : "Read this paragraph aloud"}
                          aria-label={isThisParagraphSpeaking ? "Stop reading this paragraph" : "Read this paragraph aloud"}
                        >
                          {isThisParagraphSpeaking ? <StopIcon className="h-3 w-3 text-white" /> : <PlayIcon className="h-3 w-3 text-white" />}
                        </Button>
                      </div>
                    );
                  })}
                </div>
              ) : typeof currentItemForDisplay === 'string' ? (
                 // Fallback for text content if paragraph splitting isn't working or not applicable
                 currentItemForDisplay
              ) : (
                "Loading page content..."
              )}
            </div>
        )}
      </div>

      {fileResult?.type === 'text' && textContentLang === 'ar' && aiInstance && typeof currentItemForDisplay === 'string' && (
        <ArabicTextNormalizer
          text={currentItemForDisplay}
          ai={aiInstance}
        />
      )}

      {totalPages > 0 && (
        <div className="flex justify-between items-center pt-4 border-t border-slate-600 flex-shrink-0">
          <Button onClick={goToPrevPage} disabled={currentPage === 0} className="bg-sky-600 hover:bg-sky-500">
            Previous Page
          </Button>
          <p className="text-sm text-slate-400" aria-live="polite">
            Page {currentPage + 1} of {totalPages}
          </p>
          <Button onClick={goToNextPage} disabled={currentPage >= totalPages - 1} className="bg-sky-600 hover:bg-sky-500">
            Next Page
          </Button>
        </div>
      )}
      <p className="text-sm text-slate-400 italic mt-2 text-center flex-shrink-0">
        {isPdfVisual ? "Interaction with PDF page content via AI uses the full extracted text." : "Select text on the current page to interact with it, or use paragraph play buttons."}
      </p>
    </div>
  );
};
