
import React, { useState, useEffect, useMemo } from 'react';
import type { Presentation, Slide, InteractionType } from '../types';
import { MermaidDiagram } from './MermaidDiagram';
import { Button } from './common/Button';

// Simple SVG Icons for Navigation
const ChevronLeftIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`w-5 h-5 ${className}`}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
  </svg>
);
const ChevronRightIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`w-5 h-5 ${className}`}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
  </svg>
);


interface InteractiveElementProps {
  interaction: InteractionType;
  slideId: string; // For unique quiz radio names
}

const InteractiveElement: React.FC<InteractiveElementProps> = ({ interaction, slideId }) => {
  const [userAnswer, setUserAnswer] = useState<string>('');
  const [showResult, setShowResult] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  useEffect(() => {
    // Reset quiz state when interaction changes (e.g., new slide)
    setUserAnswer('');
    setShowResult(false);
    setSelectedOption(null);
  }, [interaction]);

  if (interaction.type === 'video' && interaction.videoUrl) {
    const youtubeSearchUrl = interaction.videoSearchQuery 
      ? `https://www.youtube.com/results?search_query=${encodeURIComponent(interaction.videoSearchQuery)}`
      : null;

    return (
      <div className="my-6 p-4 bg-slate-800 rounded-lg shadow">
        <h4 className="text-lg font-semibold text-sky-400 mb-3">Related Video Content</h4>
        <div className="aspect-w-16 aspect-h-9 mb-3">
          <video controls width="100%" className="rounded-md shadow-inner" key={interaction.videoUrl + slideId}>
            <source src={interaction.videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
        {youtubeSearchUrl && (
          <a 
            href={youtubeSearchUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="inline-block text-sm text-sky-400 hover:text-sky-300 mt-2 underline transition-colors duration-150"
          >
            Search YouTube: "{interaction.videoSearchQuery}"
          </a>
        )}
      </div>
    );
  }

  if (interaction.type === 'quiz' && interaction.question && interaction.options) {
    const handleSubmit = () => {
      if (selectedOption) {
        setUserAnswer(selectedOption);
        setShowResult(true);
      }
    };
    return (
      <div className="my-6 p-6 bg-slate-800 rounded-lg shadow">
        <h4 className="text-lg font-semibold text-sky-400 mb-1">Quiz Time!</h4>
        <p className="text-slate-300 mb-4">{interaction.question}</p>
        <div className="space-y-3">
          {interaction.options.map((option, index) => (
            <label 
              key={index} 
              className={`flex items-center space-x-3 p-3 rounded-md border transition-all duration-150 cursor-pointer
                ${selectedOption === option ? 'bg-sky-700 border-sky-500 ring-2 ring-sky-500' : 'bg-slate-700 border-slate-600 hover:bg-slate-600 hover:border-slate-500'}
                ${showResult && option === interaction.correctAnswer ? '!bg-green-700 !border-green-500' : ''}
                ${showResult && selectedOption === option && option !== interaction.correctAnswer ? '!bg-red-700 !border-red-500' : ''}
              `}
            >
              <input 
                type="radio" 
                name={`quizOption-${slideId}`} 
                value={option} 
                onChange={(e) => setSelectedOption(e.target.value)} 
                className="form-radio appearance-none w-4 h-4 border-2 border-slate-500 rounded-full bg-slate-800 checked:bg-sky-500 checked:border-sky-500 focus:ring-offset-0 focus:ring-1 focus:ring-sky-400"
                disabled={showResult}
              />
              <span className="text-slate-200">{option}</span>
            </label>
          ))}
        </div>
        {!showResult && (
          <Button onClick={handleSubmit} disabled={!selectedOption} className="mt-4 bg-emerald-600 hover:bg-emerald-500 text-sm">
            Submit Answer
          </Button>
        )}
        {showResult && (
          <div className={`mt-4 p-3 rounded-md text-sm font-medium 
            ${userAnswer === interaction.correctAnswer ? 'bg-green-800 border border-green-600 text-green-200' : 'bg-red-800 border border-red-600 text-red-200'}`}>
            {userAnswer === interaction.correctAnswer ? 'Correct!' : `Incorrect. The correct answer is: ${interaction.correctAnswer}.`}
          </div>
        )}
      </div>
    );
  }
  return null;
};

interface PresentationViewerProps {
  presentation: Presentation;
}

export const PresentationViewer: React.FC<PresentationViewerProps> = ({ presentation }) => {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [animateElements, setAnimateElements] = useState(false);

  const goToNextSlide = () => {
    if (currentSlideIndex < presentation.slides.length - 1) {
      setAnimateElements(false);
      setCurrentSlideIndex((prev) => prev + 1);
    }
  };

  const goToPrevSlide = () => {
     if (currentSlideIndex > 0) {
      setAnimateElements(false);
      setCurrentSlideIndex((prev) => prev - 1);
    }
  };
  
  useEffect(() => {
    // Reset to first slide and trigger animation when presentation changes
    setCurrentSlideIndex(0);
    setAnimateElements(false); // Ensure animation state is reset
    const timer = setTimeout(() => setAnimateElements(true), 50); // Trigger animation shortly after
    return () => clearTimeout(timer);
  }, [presentation]);

  useEffect(() => {
    // Trigger animation for new slide content
    setAnimateElements(false); // Reset animation state
    const timer = setTimeout(() => setAnimateElements(true), 50); // Trigger after a short delay to allow rerender
    return () => clearTimeout(timer);
  }, [currentSlideIndex]);


  if (!presentation || presentation.slides.length === 0) {
    return <p className="text-slate-400">No presentation loaded or presentation is empty.</p>;
  }

  const currentSlide: Slide = presentation.slides[currentSlideIndex];
  
  const slideKey = `slide-${currentSlideIndex}-${presentation.title}`; // More robust key for re-renders

  return (
    <div className="space-y-4 md:space-y-6 h-full flex flex-col">
      <h2 className="text-3xl md:text-4xl font-bold text-sky-300 text-center tracking-tight animate-fadeInUp">
        {presentation.title}
      </h2>
      
      <div className="flex-grow bg-slate-800/70 backdrop-blur-sm p-4 md:p-6 lg:p-8 rounded-xl shadow-2xl min-h-[400px] flex flex-col overflow-hidden">
        <div key={slideKey} className="flex-grow overflow-y-auto presentation-slide-content pr-2"> {/* Added key for re-animation */}
          <h3 className={`text-2xl md:text-3xl font-semibold text-cyan-300 mb-5 ${animateElements ? 'animate-fadeInUp' : 'opacity-0'}`}>
            {currentSlide.slideTitle}
          </h3>
          
          {currentSlide.contentPoints && currentSlide.contentPoints.length > 0 && (
            <ul className="list-none space-y-3 text-slate-200 mb-6 pl-1">
              {currentSlide.contentPoints.map((point, index) => (
                <li 
                  key={`${slideKey}-point-${index}`} 
                  className={`flex items-start ${animateElements ? 'animate-fadeInUp' : 'opacity-0'}`}
                  style={{ animationDelay: `${index * 0.1 + 0.2}s` }} // Staggered animation
                >
                  <svg className="w-5 h-5 text-sky-400 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>{point}</span>
                </li>
              ))}
            </ul>
          )}

          {currentSlide.imageUrl && (
            <div 
              className={`my-6 text-center ${animateElements ? 'animate-fadeInUp' : 'opacity-0'}`}
              style={{ animationDelay: `${(currentSlide.contentPoints?.length || 0) * 0.1 + 0.3}s` }}
            >
              <img 
                src={currentSlide.imageUrl} 
                alt={currentSlide.imageSuggestion || currentSlide.slideTitle} 
                className="max-w-full md:max-w-lg mx-auto rounded-lg shadow-xl border-2 border-slate-700" 
              />
            </div>
          )}

          {currentSlide.mermaidSyntax && (
            <div 
              className={`${animateElements ? 'animate-fadeInUp' : 'opacity-0'}`}
              style={{ animationDelay: `${(currentSlide.contentPoints?.length || 0) * 0.1 + 0.4}s` }}
            >
              <MermaidDiagram chart={currentSlide.mermaidSyntax} id={`mermaid-${slideKey}`} />
            </div>
          )}
          
          {currentSlide.interaction && (
            <div 
              className={`${animateElements ? 'animate-fadeInUp' : 'opacity-0'}`}
              style={{ animationDelay: `${(currentSlide.contentPoints?.length || 0) * 0.1 + 0.5}s` }}
            >
               <InteractiveElement interaction={currentSlide.interaction} slideId={slideKey} />
            </div>
          )}
        </div>

        <div className="mt-6 flex justify-between items-center border-t border-slate-700 pt-4 flex-shrink-0">
          <Button onClick={goToPrevSlide} disabled={currentSlideIndex === 0} className="bg-sky-600 hover:bg-sky-500 flex items-center space-x-2">
            <ChevronLeftIcon /> <span>Previous</span>
          </Button>
          <div className="flex items-center space-x-2">
            {presentation.slides.map((_, index) => (
              <button
                key={`dot-${index}`}
                onClick={() => {
                  setAnimateElements(false);
                  setCurrentSlideIndex(index);
                }}
                className={`w-3 h-3 rounded-full transition-colors duration-150
                  ${index === currentSlideIndex ? 'bg-sky-400 scale-125' : 'bg-slate-600 hover:bg-slate-500'}
                `}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
          <Button onClick={goToNextSlide} disabled={currentSlideIndex === presentation.slides.length - 1} className="bg-sky-600 hover:bg-sky-500 flex items-center space-x-2">
             <span>Next</span> <ChevronRightIcon />
          </Button>
        </div>
         <p className="text-slate-500 text-xs text-center mt-2 flex-shrink-0">
            Slide {currentSlideIndex + 1} of {presentation.slides.length}
          </p>
      </div>
    </div>
  );
};
