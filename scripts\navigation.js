/**
 * Navigation and routing functionality for AI Interactive Textbook
 * Author: Dr.<PERSON>, SUST-BME
 */

// Navigation Manager
const NavigationManager = {
  currentPath: window.location.pathname,
  
  init() {
    this.setupMobileMenu();
    this.setupSmoothScrolling();
    this.setupActiveNavLinks();
    this.setupBackToTop();
    this.setupKeyboardNavigation();
    
    console.log('Navigation Manager initialized');
  },

  // Mobile menu functionality
  setupMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
      mobileMenuBtn.addEventListener('click', () => {
        this.toggleMobileMenu();
      });

      // Close mobile menu when clicking outside
      document.addEventListener('click', (e) => {
        if (!mobileMenuBtn.contains(e.target) && !mobileMenu.contains(e.target)) {
          this.closeMobileMenu();
        }
      });

      // Close mobile menu on escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          this.closeMobileMenu();
        }
      });

      // Close mobile menu when window is resized to desktop
      window.addEventListener('resize', () => {
        if (window.innerWidth >= 768) {
          this.closeMobileMenu();
        }
      });
    }
  },

  toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    
    if (mobileMenu && mobileMenuBtn) {
      const isOpen = !mobileMenu.classList.contains('hidden');
      
      if (isOpen) {
        this.closeMobileMenu();
      } else {
        this.openMobileMenu();
      }
    }
  },

  openMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    
    if (mobileMenu && mobileMenuBtn) {
      mobileMenu.classList.remove('hidden');
      mobileMenu.classList.add('animate-slideInDown');
      mobileMenuBtn.setAttribute('aria-expanded', 'true');
      
      // Update button icon to X
      const svg = mobileMenuBtn.querySelector('svg path');
      if (svg) {
        svg.setAttribute('d', 'M6 18L18 6M6 6l12 12');
      }
    }
  },

  closeMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    
    if (mobileMenu && mobileMenuBtn) {
      mobileMenu.classList.add('hidden');
      mobileMenu.classList.remove('animate-slideInDown');
      mobileMenuBtn.setAttribute('aria-expanded', 'false');
      
      // Update button icon to hamburger
      const svg = mobileMenuBtn.querySelector('svg path');
      if (svg) {
        svg.setAttribute('d', 'M4 6h16M4 12h16M4 18h16');
      }
    }
  },

  // Smooth scrolling for anchor links
  setupSmoothScrolling() {
    document.addEventListener('click', (e) => {
      const link = e.target.closest('a[href^="#"]');
      if (link) {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          this.smoothScrollTo(targetElement);
          this.closeMobileMenu(); // Close mobile menu if open
        }
      }
    });
  },

  smoothScrollTo(element, offset = 80) {
    const elementPosition = element.offsetTop - offset;
    
    window.scrollTo({
      top: elementPosition,
      behavior: 'smooth'
    });
  },

  // Active navigation link highlighting
  setupActiveNavLinks() {
    this.updateActiveNavLinks();
    
    // Update on scroll for single-page sections
    window.addEventListener('scroll', 
      window.AppUtils?.Utils?.throttle(() => {
        this.updateActiveNavLinks();
      }, 100) || (() => {})
    );
  },

  updateActiveNavLinks() {
    const navLinks = document.querySelectorAll('.nav-link');
    const currentPath = window.location.pathname;
    
    navLinks.forEach(link => {
      link.classList.remove('active');
      
      const href = link.getAttribute('href');
      
      // Check for exact path match
      if (href === currentPath || 
          (currentPath === '/' && href === '/') ||
          (currentPath.includes(href) && href !== '/')) {
        link.classList.add('active');
      }
    });

    // For single-page navigation with sections
    if (currentPath === '/' || currentPath === '/index.html') {
      this.updateSectionActiveLinks();
    }
  },

  updateSectionActiveLinks() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    
    let currentSection = '';
    
    sections.forEach(section => {
      const sectionTop = section.offsetTop - 100;
      const sectionHeight = section.offsetHeight;
      
      if (window.scrollY >= sectionTop && 
          window.scrollY < sectionTop + sectionHeight) {
        currentSection = section.getAttribute('id');
      }
    });
    
    navLinks.forEach(link => {
      link.classList.remove('active');
      const href = link.getAttribute('href').substring(1);
      
      if (href === currentSection) {
        link.classList.add('active');
      }
    });
  },

  // Back to top functionality
  setupBackToTop() {
    // Create back to top button if it doesn't exist
    let backToTopBtn = document.getElementById('back-to-top');
    
    if (!backToTopBtn) {
      backToTopBtn = document.createElement('button');
      backToTopBtn.id = 'back-to-top';
      backToTopBtn.className = 'back-to-top-btn hidden';
      backToTopBtn.innerHTML = `
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
      `;
      backToTopBtn.setAttribute('aria-label', 'Back to top');
      backToTopBtn.setAttribute('title', 'Back to top');
      document.body.appendChild(backToTopBtn);
    }

    // Show/hide based on scroll position
    window.addEventListener('scroll', 
      window.AppUtils?.Utils?.throttle(() => {
        if (window.scrollY > 300) {
          backToTopBtn.classList.remove('hidden');
          backToTopBtn.classList.add('animate-fadeIn');
        } else {
          backToTopBtn.classList.add('hidden');
          backToTopBtn.classList.remove('animate-fadeIn');
        }
      }, 100) || (() => {})
    );

    // Scroll to top on click
    backToTopBtn.addEventListener('click', () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  },

  // Keyboard navigation support
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // Alt + H for home
      if (e.altKey && e.key === 'h') {
        e.preventDefault();
        window.location.href = '/';
      }
      
      // Alt + M for mobile menu toggle
      if (e.altKey && e.key === 'm') {
        e.preventDefault();
        this.toggleMobileMenu();
      }
      
      // Alt + T for theme toggle
      if (e.altKey && e.key === 't') {
        e.preventDefault();
        if (window.AppUtils?.ThemeManager) {
          window.AppUtils.ThemeManager.toggleTheme();
        }
      }
    });

    // Focus management for accessibility
    this.setupFocusManagement();
  },

  setupFocusManagement() {
    // Skip to main content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.className = 'skip-link';
    skipLink.textContent = 'Skip to main content';
    skipLink.addEventListener('click', (e) => {
      e.preventDefault();
      const mainContent = document.getElementById('main-content') || document.getElementById('root');
      if (mainContent) {
        mainContent.focus();
        mainContent.scrollIntoView();
      }
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);

    // Focus trap for mobile menu
    document.addEventListener('keydown', (e) => {
      const mobileMenu = document.getElementById('mobile-menu');
      if (mobileMenu && !mobileMenu.classList.contains('hidden') && e.key === 'Tab') {
        this.trapFocus(e, mobileMenu);
      }
    });
  },

  trapFocus(e, container) {
    const focusableElements = container.querySelectorAll(
      'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select'
    );
    
    const firstFocusableElement = focusableElements[0];
    const lastFocusableElement = focusableElements[focusableElements.length - 1];

    if (e.shiftKey) {
      if (document.activeElement === firstFocusableElement) {
        lastFocusableElement.focus();
        e.preventDefault();
      }
    } else {
      if (document.activeElement === lastFocusableElement) {
        firstFocusableElement.focus();
        e.preventDefault();
      }
    }
  },

  // Page transition effects
  navigateWithTransition(url) {
    if (window.AppUtils?.LoadingManager) {
      window.AppUtils.LoadingManager.show('Loading page...');
    }
    
    // Add page transition class
    document.body.classList.add('page-transition-exit');
    
    setTimeout(() => {
      window.location.href = url;
    }, 300);
  },

  // Breadcrumb generation
  generateBreadcrumbs() {
    const path = window.location.pathname;
    const segments = path.split('/').filter(segment => segment);
    
    const breadcrumbContainer = document.getElementById('breadcrumbs');
    if (!breadcrumbContainer) return;

    let breadcrumbHTML = '<a href="/" class="breadcrumb-link">Home</a>';
    let currentPath = '';

    segments.forEach((segment, index) => {
      currentPath += '/' + segment;
      const isLast = index === segments.length - 1;
      const displayName = segment.charAt(0).toUpperCase() + segment.slice(1).replace('-', ' ');
      
      if (isLast) {
        breadcrumbHTML += ` <span class="breadcrumb-separator">></span> <span class="breadcrumb-current">${displayName}</span>`;
      } else {
        breadcrumbHTML += ` <span class="breadcrumb-separator">></span> <a href="${currentPath}" class="breadcrumb-link">${displayName}</a>`;
      }
    });

    breadcrumbContainer.innerHTML = breadcrumbHTML;
  }
};

// Initialize navigation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  NavigationManager.init();
});

// Export for global use
window.NavigationManager = NavigationManager;
