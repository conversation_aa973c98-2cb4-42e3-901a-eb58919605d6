
import type { ParsedFileResult, ParsedPageImage } from '../types';

// Ensure pdfjsLib and mammoth are available globally from CDN
declare global {
  interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    pdfjsLib?: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mammoth?: any;
    pdfjsWorkerSrc?: string; 
  }
}

export const parseFile = async (file: File): Promise<ParsedFileResult> => {
  const fileName = file.name.toLowerCase();
  const fileType = file.type;

  if (fileName.endsWith('.txt') || fileType === 'text/plain') {
    const content = await file.text();
    return { type: 'text', content, fileName: file.name };
  } else if (fileName.endsWith('.pdf') || fileType === 'application/pdf') {
    if (!window.pdfjsLib) {
      throw new Error("pdf.js library is not loaded. Please ensure it's included via CDN.");
    }

    if (window.pdfjsWorkerSrc) {
        window.pdfjsLib.GlobalWorkerOptions.workerSrc = window.pdfjsWorkerSrc;
    } else {
      console.error("CRITICAL: window.pdfjsWorkerSrc is not defined. PDF processing will likely fail or hang. Check index.html.");
      throw new Error("Configuration error: pdf.js worker source (window.pdfjsWorkerSrc) is not defined.");
    }

    const arrayBuffer = await file.arrayBuffer();
    const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    let fullTextContent = '';
    const pageImages: ParsedPageImage[] = [];
    const RENDER_SCALE = 1.5; // Adjust for quality/performance. Higher scale = better quality, larger images, slower.

    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      
      // Extract text content
      const textData = await page.getTextContent();
      fullTextContent += textData.items
        .filter((item: any) => typeof item.str === 'string')
        .map((item: any) => item.str)
        .join(' ') + '\n';

      // Render page to image
      const viewport = page.getViewport({ scale: RENDER_SCALE });
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      if (context) {
        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };
        await page.render(renderContext).promise;
        // Use JPEG for smaller file sizes, adjust quality (0.0 to 1.0)
        pageImages.push({ 
          dataUrl: canvas.toDataURL('image/jpeg', 0.85), 
          width: viewport.width, 
          height: viewport.height 
        });
      }
      canvas.remove(); // Clean up offscreen canvas
    }
    return { type: 'pdf_visual', pages: pageImages, fullText: fullTextContent, fileName: file.name };
  } else if (fileName.endsWith('.docx') || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    if (!window.mammoth) {
        throw new Error("Mammoth.js library is not loaded. Please ensure it's included via CDN.");
    }
    const arrayBuffer = await file.arrayBuffer();
    const result = await window.mammoth.extractRawText({ arrayBuffer });
    return { type: 'text', content: result.value, fileName: file.name };
  } else {
    throw new Error(`Unsupported file type: ${fileName}. Please upload TXT, PDF, or DOCX.`);
  }
};
