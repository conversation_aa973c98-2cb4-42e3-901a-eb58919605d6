/* Component-specific Styles for AI Interactive Textbook */

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
  padding: 6rem 0 4rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23334155" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.1;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #38bdf8, #06b6d4, #0891b2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--secondary-text);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Feature Cards */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.feature-card {
  background: var(--secondary-bg);
  border-radius: 16px;
  padding: 2.5rem;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-text), var(--hover-accent));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
  border-color: var(--accent-text);
}

.feature-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, var(--accent-text), var(--hover-accent));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--accent-text);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--secondary-text);
  line-height: 1.6;
}

/* Documentation Styles */
.doc-nav {
  background: var(--secondary-bg);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  position: sticky;
  top: 100px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

.doc-nav-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--accent-text);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.doc-nav-list {
  list-style: none;
}

.doc-nav-item {
  margin-bottom: 0.5rem;
}

.doc-nav-link {
  display: block;
  padding: 0.5rem 0.75rem;
  color: var(--secondary-text);
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.doc-nav-link:hover,
.doc-nav-link.active {
  background: var(--accent-bg);
  color: var(--accent-text);
  transform: translateX(4px);
}

.doc-content {
  background: var(--secondary-bg);
  border-radius: 12px;
  padding: 2.5rem;
  border: 1px solid var(--border-color);
}

.doc-section {
  margin-bottom: 3rem;
}

.doc-section:last-child {
  margin-bottom: 0;
}

.doc-section h2 {
  color: var(--accent-text);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
}

.doc-section h3 {
  color: var(--primary-text);
  margin-top: 2rem;
  margin-bottom: 1rem;
}

/* Code Blocks */
.code-block {
  background: var(--primary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.code-inline {
  background: var(--accent-bg);
  color: var(--accent-text);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

/* Alert Components */
.alert {
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin: 1.5rem 0;
  border-left: 4px solid;
}

.alert-info {
  background: rgba(56, 189, 248, 0.1);
  border-left-color: var(--accent-text);
  color: var(--accent-text);
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  border-left-color: var(--success-color);
  color: var(--success-color);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: var(--warning-color);
  color: var(--warning-color);
}

.alert-error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: var(--error-color);
  color: var(--error-color);
}

/* Contact Form */
.contact-form {
  background: var(--secondary-bg);
  border-radius: 16px;
  padding: 2.5rem;
  border: 1px solid var(--border-color);
  max-width: 600px;
  margin: 0 auto;
}

.contact-form-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--accent-text);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}

/* About Page Styles */
.about-section {
  margin-bottom: 4rem;
}

.about-image {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.about-image:hover {
  transform: scale(1.02);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.stat-card {
  text-align: center;
  background: var(--secondary-bg);
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--accent-text);
  display: block;
}

.stat-label {
  color: var(--secondary-text);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.5rem;
}

/* Timeline Component */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--border-color);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  background: var(--secondary-bg);
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -1.75rem;
  top: 1.5rem;
  width: 12px;
  height: 12px;
  background: var(--accent-text);
  border-radius: 50%;
  border: 3px solid var(--secondary-bg);
}

.timeline-date {
  color: var(--accent-text);
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.timeline-title {
  color: var(--primary-text);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.timeline-description {
  color: var(--secondary-text);
  line-height: 1.6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .feature-card {
    padding: 2rem;
  }
  
  .doc-content {
    padding: 1.5rem;
  }
  
  .contact-form {
    padding: 2rem;
  }
  
  .timeline {
    padding-left: 1.5rem;
  }
  
  .timeline-item::before {
    left: -1.5rem;
  }
}
