
export interface Topic {
  id: string;
  title: string;
  summary: string;
}

export interface SlideContentPoint {
  text: string;
  subPoints?: string[];
}

export interface InteractionType {
  type: 'quiz' | 'video' | 'audio';
  question?: string;
  options?: string[];
  correctAnswer?: string;
  videoUrl?: string; 
  videoSearchQuery?: string; 
  audioUrl?: string;
}

export interface Slide {
  slideTitle: string;
  contentPoints: string[]; 
  imageSuggestion?: string | null; 
  diagramSuggestion?: { type: string; description: string } | null;
  imageUrl?: string; 
  mermaidSyntax?: string; 
  interaction?: InteractionType;
}

export interface Presentation {
  title: string;
  slides: Slide[];
}

export interface GeminiChatSession {
  sendMessage: (message: string) => Promise<string>;
}

export interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

// Speech Recognition API Types
export interface SpeechRecognitionAlternative {
  readonly transcript: string;
  readonly confidence: number;
}

export interface SpeechRecognitionResult {
  readonly isFinal: boolean;
  readonly length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
}

export interface SpeechRecognitionResultList {
  readonly length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

export interface SpeechRecognitionEvent extends Event {
  readonly resultIndex: number;
  readonly results: SpeechRecognitionResultList;
}

export interface SpeechRecognitionErrorEvent extends Event {
  readonly error: string; 
  readonly message: string;
}

export interface SpeechRecognitionStatic {
  new(): SpeechRecognition;
}

export interface SpeechRecognition extends EventTarget {
  lang: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
  grammars?: any; 

  onaudiostart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onaudioend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null;
  onnomatch: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onsoundstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onsoundend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;

  abort(): void;
  start(): void;
  stop(): void;
}

declare var SpeechRecognition: SpeechRecognitionStatic | undefined;
declare var webkitSpeechRecognition: SpeechRecognitionStatic | undefined;

// Parsed File Content Types
export interface ParsedPageImage {
  dataUrl: string; // image data URL (e.g., "data:image/jpeg;base64,...")
  width: number;   // original width of the rendered page
  height: number;  // original height of the rendered page
}

export type ParsedFileResult = 
  | { type: 'text'; content: string; fileName: string; }
  | { type: 'pdf_visual'; pages: ParsedPageImage[]; fullText: string; fileName: string; };

// Arabic Text Normalization Result
export interface NormalizationResult {
  originalText: string;
  normalizedText: string;
  analysis: string; // Gemini's analysis of changes or text quality
}

declare global {
  interface Window {
    SpeechRecognition?: SpeechRecognitionStatic;
    webkitSpeechRecognition?: SpeechRecognitionStatic;
    // pdfjsLib, mammoth, pdfjsWorkerSrc, mermaid are already in App.tsx global
  }
}
