<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Features - AI Interactive Textbook</title>
  
  <!-- SEO and Basic Meta -->
  <meta name="description" content="Discover the powerful features of AI Interactive Textbook: document upload, AI conversations, topic extraction, presentation generation, and voice interaction.">
  <meta name="author" content="Dr<PERSON>il, SUST-BME">
  
  <!-- Theme Color for Mobile Browsers -->
  <meta name="theme-color" content="#0f172a">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://example.com/pages/features.html">
  <meta property="og:title" content="Features - AI Interactive Textbook">
  <meta property="og:description" content="Discover the powerful features of AI Interactive Textbook: document upload, AI conversations, topic extraction, presentation generation, and voice interaction.">
  <meta property="og:site_name" content="AI Interactive Textbook">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://example.com/pages/features.html">
  <meta property="twitter:title" content="Features - AI Interactive Textbook">
  <meta property="twitter:description" content="Discover the powerful features of AI Interactive Textbook: document upload, AI conversations, topic extraction, presentation generation, and voice interaction.">

  <!-- Favicon Placeholders -->
  <link rel="apple-touch-icon" sizes="180x180" href="../apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="../favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="../favicon-16x16.png">
  <link rel="manifest" href="../site.webmanifest">

  <!-- External CSS and JS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="../styles/main.css">
  <link rel="stylesheet" href="../styles/components.css">
  <link rel="stylesheet" href="../styles/animations.css">
</head>
<body class="bg-slate-900 text-slate-100">
  <!-- Navigation Bar -->
  <nav id="main-nav" class="fixed top-0 left-0 right-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
    <div class="container mx-auto px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <a href="../index.html" class="text-xl font-bold text-sky-400 hover:text-sky-300 transition-colors">
            AI Interactive Textbook
          </a>
        </div>
        <div class="hidden md:flex items-center space-x-6">
          <a href="../index.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">Home</a>
          <a href="features.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors active">Features</a>
          <a href="documentation.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">Documentation</a>
          <a href="about.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">About</a>
          <a href="contact.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors">Contact</a>
        </div>
        <button type="button" id="mobile-menu-btn" class="md:hidden text-slate-300 hover:text-sky-400" title="Toggle mobile menu" aria-label="Toggle mobile menu">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
      <!-- Mobile Menu -->
      <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4 border-t border-slate-700">
        <div class="flex flex-col space-y-2 pt-4">
          <a href="../index.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">Home</a>
          <a href="features.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2 active">Features</a>
          <a href="documentation.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">Documentation</a>
          <a href="about.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">About</a>
          <a href="contact.html" class="nav-link text-slate-300 hover:text-sky-400 transition-colors py-2">Contact</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="pt-20" id="main-content">
    <!-- Hero Section -->
    <section class="hero">
      <div class="container mx-auto px-4">
        <div class="hero-content">
          <h1 class="hero-title animate-fadeInUp">Powerful Features</h1>
          <p class="hero-subtitle animate-fadeInUp delay-200">
            Discover the comprehensive set of tools designed to revolutionize your learning experience
          </p>
        </div>
      </div>
    </section>

    <!-- Features Grid -->
    <section class="section">
      <div class="container mx-auto px-4">
        <div class="feature-grid">
          <!-- Document Upload -->
          <div class="feature-card fade-in-up" data-delay="100">
            <div class="feature-icon">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
            </div>
            <h3 class="feature-title">Multi-Format Document Upload</h3>
            <p class="feature-description">
              Upload and process documents in multiple formats including TXT, PDF, and DOCX. Our advanced parsing engine extracts text content while preserving formatting and structure.
            </p>
          </div>

          <!-- AI Conversations -->
          <div class="feature-card fade-in-up" data-delay="200">
            <div class="feature-icon">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
            <h3 class="feature-title">AI-Powered Conversations</h3>
            <p class="feature-description">
              Engage in intelligent conversations about your document content. Ask questions, get explanations, and explore topics with our advanced AI assistant powered by Google Gemini.
            </p>
          </div>

          <!-- Topic Extraction -->
          <div class="feature-card fade-in-up" data-delay="300">
            <div class="feature-icon">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            </div>
            <h3 class="feature-title">Intelligent Topic Extraction</h3>
            <p class="feature-description">
              Automatically identify and extract key topics from your documents. Our AI analyzes content structure and meaning to provide comprehensive topic summaries and insights.
            </p>
          </div>

          <!-- Presentation Generation -->
          <div class="feature-card fade-in-up" data-delay="400">
            <div class="feature-icon">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0V4a1 1 0 011-1h6a1 1 0 011 1v3M7 7h10"></path>
              </svg>
            </div>
            <h3 class="feature-title">Dynamic Presentation Creation</h3>
            <p class="feature-description">
              Transform extracted topics into engaging presentations with visual elements, diagrams, and interactive content. Perfect for teaching, studying, or sharing knowledge.
            </p>
          </div>

          <!-- Voice Interaction -->
          <div class="feature-card fade-in-up" data-delay="500">
            <div class="feature-icon">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
              </svg>
            </div>
            <h3 class="feature-title">Voice Interaction & TTS</h3>
            <p class="feature-description">
              Experience hands-free interaction with voice commands and text-to-speech capabilities. Listen to your documents or have natural voice conversations about the content.
            </p>
          </div>

          <!-- Visual Content -->
          <div class="feature-card fade-in-up" data-delay="600">
            <div class="feature-icon">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="feature-title">Rich Visual Content</h3>
            <p class="feature-description">
              Enhance understanding with automatically generated diagrams, charts, and visual aids. Mermaid diagrams and contextual images bring your content to life.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Feature Details -->
    <section class="section bg-slate-800/50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-sky-400 mb-4 fade-in-up">Advanced Capabilities</h2>
          <p class="text-xl text-slate-300 max-w-3xl mx-auto fade-in-up delay-200">
            Built with cutting-edge technology to provide the most comprehensive learning experience
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div class="card fade-in-left">
            <h3 class="text-xl font-semibold text-sky-400 mb-4">Smart Document Processing</h3>
            <ul class="space-y-3 text-slate-300">
              <li class="flex items-start">
                <span class="text-sky-400 mr-2">•</span>
                Advanced PDF parsing with visual content extraction
              </li>
              <li class="flex items-start">
                <span class="text-sky-400 mr-2">•</span>
                DOCX document structure preservation
              </li>
              <li class="flex items-start">
                <span class="text-sky-400 mr-2">•</span>
                Text normalization and cleaning
              </li>
              <li class="flex items-start">
                <span class="text-sky-400 mr-2">•</span>
                Multi-language support
              </li>
            </ul>
          </div>

          <div class="card fade-in-right">
            <h3 class="text-xl font-semibold text-sky-400 mb-4">AI-Powered Analysis</h3>
            <ul class="space-y-3 text-slate-300">
              <li class="flex items-start">
                <span class="text-sky-400 mr-2">•</span>
                Context-aware topic identification
              </li>
              <li class="flex items-start">
                <span class="text-sky-400 mr-2">•</span>
                Semantic content understanding
              </li>
              <li class="flex items-start">
                <span class="text-sky-400 mr-2">•</span>
                Intelligent summarization
              </li>
              <li class="flex items-start">
                <span class="text-sky-400 mr-2">•</span>
                Real-time conversation capabilities
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="section">
      <div class="container mx-auto px-4 text-center">
        <div class="max-w-2xl mx-auto">
          <h2 class="text-3xl font-bold text-sky-400 mb-4 fade-in-up">Ready to Transform Your Learning?</h2>
          <p class="text-xl text-slate-300 mb-8 fade-in-up delay-200">
            Experience the future of interactive learning with AI-powered document analysis and conversation.
          </p>
          <div class="space-x-4 fade-in-up delay-400">
            <a href="../index.html" class="btn btn-primary">Get Started</a>
            <a href="documentation.html" class="btn btn-outline">Learn More</a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-slate-800 border-t border-slate-700 py-8">
    <div class="container mx-auto px-4 text-center">
      <div class="text-slate-400">
        <p>&copy; 2024 AI Interactive Textbook. Created by Dr.Mohammed Yagoub Esmail, SUST-BME</p>
        <p class="mt-2">Email: <EMAIL> | Phone: +249912867327, +966538076790</p>
      </div>
    </div>
  </footer>

  <!-- JavaScript Files -->
  <script src="../scripts/main.js"></script>
  <script src="../scripts/navigation.js"></script>
  <script src="../scripts/animations.js"></script>
</body>
</html>
